/// 共鸣引擎服务
/// 负责接收STT流、分析规则并触发AI回应

import 'dart:async';
import 'dart:collection';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../stt/streaming_stt_service.dart';
import '../stt/stt_models.dart';
import 'resonance_models.dart';
import 'resonance_rule_loader.dart';
import 'condition_evaluator.dart';

/// 对话上下文项
class ConversationContextItem {
  final String text;
  final DateTime timestamp;
  final bool isFinal;
  final double confidence;

  const ConversationContextItem({
    required this.text,
    required this.timestamp,
    required this.isFinal,
    required this.confidence,
  });

  factory ConversationContextItem.fromSttResult(SttResult result) {
    return ConversationContextItem(
      text: result.text,
      timestamp: result.timestamp,
      isFinal: result.isFinal,
      confidence: result.confidence,
    );
  }
}

/// 触发的回应事件
class ResonanceResponseEvent {
  final String ruleId;
  final String ruleName;
  final ResonanceActionType actionType;
  final String responseText;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  const ResonanceResponseEvent({
    required this.ruleId,
    required this.ruleName,
    required this.actionType,
    required this.responseText,
    required this.timestamp,
    required this.metadata,
  });
}

/// 共鸣引擎服务状态
enum ResonanceEngineState {
  /// 未初始化
  uninitialized,
  
  /// 已初始化但未启动
  initialized,
  
  /// 正在运行
  running,
  
  /// 已停止
  stopped,
  
  /// 错误状态
  error,
}

/// 共鸣引擎服务
class ResonanceEngineService extends ChangeNotifier {
  static const Duration _defaultContextDuration = Duration(seconds: 30);
  static const Duration _defaultCooldownDuration = Duration(seconds: 10);
  
  // 服务状态
  ResonanceEngineState _state = ResonanceEngineState.uninitialized;
  String? _errorMessage;
  
  // 规则配置
  ResonanceRuleSet? _ruleSet;
  final List<ResonanceRule> _activeRules = [];
  
  // STT服务订阅
  StreamingSttService? _sttService;
  StreamSubscription<SttResult>? _sttSubscription;
  
  // 对话上下文
  final Queue<ConversationContextItem> _contextQueue = Queue();
  final Duration _contextDuration;
  
  // 冷却机制
  DateTime? _lastResponseTime;
  final Duration _cooldownDuration;
  
  // 事件流控制器
  final StreamController<ResonanceResponseEvent> _responseController = 
      StreamController<ResonanceResponseEvent>.broadcast();
  
  // 随机数生成器
  final Random _random = Random();

  /// 构造函数
  ResonanceEngineService({
    Duration contextDuration = _defaultContextDuration,
    Duration cooldownDuration = _defaultCooldownDuration,
  }) : _contextDuration = contextDuration,
       _cooldownDuration = cooldownDuration;

  // Getters
  ResonanceEngineState get state => _state;
  String? get errorMessage => _errorMessage;
  ResonanceRuleSet? get ruleSet => _ruleSet;
  List<ResonanceRule> get activeRules => List.unmodifiable(_activeRules);
  List<ConversationContextItem> get context => List.unmodifiable(_contextQueue);
  bool get isInCooldown => _lastResponseTime != null && 
      DateTime.now().difference(_lastResponseTime!) < _cooldownDuration;
  
  /// 回应事件流
  Stream<ResonanceResponseEvent> get onResponse => _responseController.stream;

  /// 初始化服务
  Future<bool> initialize({String? configPath}) async {
    try {
      _setState(ResonanceEngineState.uninitialized);
      
      // 加载规则配置
      final loadResult = configPath != null 
          ? await ResonanceRuleLoader.loadRulesFromAsset(configPath)
          : await ResonanceRuleLoader.loadDefaultRules();
      
      if (!loadResult.success) {
        _setError('Failed to load rules: ${loadResult.errorMessage}');
        return false;
      }
      
      _ruleSet = loadResult.ruleSet!;
      _loadActiveRules();
      
      _setState(ResonanceEngineState.initialized);
      debugPrint('ResonanceEngineService initialized with ${_activeRules.length} active rules');
      
      return true;
    } catch (e) {
      _setError('Initialization failed: $e');
      return false;
    }
  }

  /// 启动服务
  Future<void> start(StreamingSttService sttService) async {
    if (_state != ResonanceEngineState.initialized) {
      throw StateError('Service must be initialized before starting');
    }
    
    try {
      _sttService = sttService;
      
      // 订阅STT结果流
      _sttSubscription = _sttService!.onResult.listen(
        _handleSttResult,
        onError: _handleSttError,
      );
      
      _setState(ResonanceEngineState.running);
      debugPrint('ResonanceEngineService started');
    } catch (e) {
      _setError('Failed to start service: $e');
      rethrow;
    }
  }

  /// 停止服务
  Future<void> stop() async {
    try {
      await _sttSubscription?.cancel();
      _sttSubscription = null;
      _sttService = null;
      
      _contextQueue.clear();
      _lastResponseTime = null;
      
      _setState(ResonanceEngineState.stopped);
      debugPrint('ResonanceEngineService stopped');
    } catch (e) {
      _setError('Failed to stop service: $e');
    }
  }

  /// 加载规则
  Future<void> loadRules(ResonanceRuleSet ruleSet) async {
    _ruleSet = ruleSet;
    _loadActiveRules();
    notifyListeners();
    debugPrint('Loaded ${_activeRules.length} active rules');
  }

  /// 手动添加上下文（用于测试）
  void addContext(String text, {DateTime? timestamp, bool isFinal = true}) {
    final item = ConversationContextItem(
      text: text,
      timestamp: timestamp ?? DateTime.now(),
      isFinal: isFinal,
      confidence: 1.0,
    );
    
    _addToContext(item);
  }

  /// 清除上下文
  void clearContext() {
    _contextQueue.clear();
    notifyListeners();
  }

  /// 重置冷却状态
  void resetCooldown() {
    _lastResponseTime = null;
    notifyListeners();
  }

  /// 设置状态（仅用于测试）
  @visibleForTesting
  void setStateForTesting(ResonanceEngineState state) {
    _setState(state);
  }

  @override
  void dispose() {
    // 直接清理资源，不调用stop()以避免在dispose后调用notifyListeners
    _sttSubscription?.cancel();
    _sttSubscription = null;
    _sttService = null;
    _contextQueue.clear();
    _lastResponseTime = null;
    _responseController.close();
    super.dispose();
  }

  // 私有方法

  void _setState(ResonanceEngineState newState) {
    if (_state != newState) {
      _state = newState;
      _errorMessage = null;
      notifyListeners();
    }
  }

  void _setError(String error) {
    _state = ResonanceEngineState.error;
    _errorMessage = error;
    debugPrint('ResonanceEngineService error: $error');
    notifyListeners();
  }

  void _loadActiveRules() {
    _activeRules.clear();
    if (_ruleSet != null) {
      _activeRules.addAll(_ruleSet!.rules.where((rule) => rule.enabled));
      _activeRules.sort((a, b) => b.priority.compareTo(a.priority)); // 高优先级在前
    }
  }

  void _handleSttResult(SttResult result) {
    try {
      // 只处理最终结果，避免中间结果造成干扰
      if (!result.isFinal) {
        return;
      }
      
      final contextItem = ConversationContextItem.fromSttResult(result);
      _addToContext(contextItem);
      
      debugPrint('STT Result: "${result.text}" at ${result.timestamp}');
    } catch (e) {
      debugPrint('Error handling STT result: $e');
    }
  }

  void _handleSttError(dynamic error) {
    debugPrint('STT Error in ResonanceEngine: $error');
  }

  void _addToContext(ConversationContextItem item) {
    _contextQueue.add(item);
    _cleanupOldContext();
    
    // 触发规则评估
    _evaluateRules();
    
    notifyListeners();
  }

  void _cleanupOldContext() {
    final cutoffTime = DateTime.now().subtract(_contextDuration);
    while (_contextQueue.isNotEmpty && 
           _contextQueue.first.timestamp.isBefore(cutoffTime)) {
      _contextQueue.removeFirst();
    }
  }

  void _evaluateRules() async {
    if (isInCooldown) {
      debugPrint('In cooldown, skipping rule evaluation');
      return;
    }

    if (_activeRules.isEmpty || _contextQueue.isEmpty) {
      return;
    }

    try {
      // 使用条件评估器评估所有规则（现在支持AI分析）
      final triggeredResults = await ConditionEvaluator.evaluateRules(
        _activeRules,
        List.unmodifiable(_contextQueue),
      );

      if (triggeredResults.isNotEmpty) {
        // 选择优先级最高的规则触发回应
        final topResult = triggeredResults.first;
        debugPrint('Rule triggered: ${topResult.rule.name} (confidence: ${topResult.evaluationResult.confidence})');

        _triggerResponse(topResult.rule);
      }
    } catch (e) {
      debugPrint('Error evaluating rules: $e');
    }
  }

  String _selectRandomResponse(List<String> responsePool) {
    if (responsePool.isEmpty) {
      return '嗯';
    }
    return responsePool[_random.nextInt(responsePool.length)];
  }

  void _triggerResponse(ResonanceRule rule) {
    final responseText = _selectRandomResponse(rule.action.responsePool);
    final event = ResonanceResponseEvent(
      ruleId: rule.id,
      ruleName: rule.name,
      actionType: rule.action.type,
      responseText: responseText,
      timestamp: DateTime.now(),
      metadata: rule.action.metadata,
    );
    
    _lastResponseTime = DateTime.now();
    _responseController.add(event);
    
    debugPrint('Triggered response: "$responseText" from rule "${rule.name}"');
    notifyListeners();
  }
}
