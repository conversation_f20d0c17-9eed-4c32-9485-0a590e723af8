/// 快速测试AI陪伴编排器修复

import 'dart:async';
import 'dart:typed_data';
import 'lib/services/ai_companion_orchestrator.dart';
import 'lib/services/stt/streaming_stt_service.dart';
import 'lib/services/stt/stt_models.dart';

/// 简单的Mock STT服务
class TestSttService extends StreamingSttService {
  final StreamController<SttResult> _resultController = StreamController<SttResult>.broadcast();
  final StreamController<SttError> _errorController = StreamController<SttError>.broadcast();
  final StreamController<SttConnectionState> _stateController = StreamController<SttConnectionState>.broadcast();
  final StreamController<void> _endOfSpeechController = StreamController<void>.broadcast();
  
  SttConnectionState _state = SttConnectionState.disconnected;
  bool _isListening = false;

  @override
  SttConnectionState get connectionState => _state;

  @override
  SttConfig get config => SttConfig(
    language: 'zh-cn',
    sampleRate: 16000,
    enableInterimResults: true,
  );

  @override
  SttStats get stats => SttStats(
    totalDuration: 1000,
    totalCharacters: 100,
    averageConfidence: 0.9,
    networkLatency: 100,
    errorCount: 0,
  );

  @override
  bool get isListening => _isListening;

  @override
  Stream<SttResult> get onResult => _resultController.stream;

  @override
  Stream<SttError> get onError => _errorController.stream;

  @override
  Stream<SttConnectionState> get onConnectionStateChanged => _stateController.stream;

  @override
  Stream<void> get onEndOfSpeech => _endOfSpeechController.stream;

  @override
  Future<bool> initialize(SttConfig config) async {
    _state = SttConnectionState.connected;
    _stateController.add(_state);
    return true;
  }

  @override
  Future<void> startListening() async {
    _isListening = true;
    _state = SttConnectionState.listening;
    _stateController.add(_state);
  }

  @override
  Future<void> stopListening() async {
    _isListening = false;
    _state = SttConnectionState.connected;
    _stateController.add(_state);
  }

  @override
  void sendAudioData(Uint8List audioData) {}

  @override
  Future<void> reset() async {
    _isListening = false;
    _state = SttConnectionState.connected;
    _stateController.add(_state);
  }

  @override
  Future<void> disconnect() async {
    _isListening = false;
    _state = SttConnectionState.disconnected;
    _stateController.add(_state);
  }

  @override
  Future<List<String>> getSupportedLanguages() async {
    return ['zh-CN', 'en-US'];
  }

  @override
  Future<int> testConnection() async {
    return 100;
  }

  void simulateResult(String text, {bool isFinal = false}) {
    final result = SttResult(
      text: text,
      isFinal: isFinal,
      confidence: 0.9,
      timestamp: DateTime.now(),
    );
    _resultController.add(result);
  }

  @override
  void dispose() {
    _resultController.close();
    _errorController.close();
    _stateController.close();
    _endOfSpeechController.close();
    super.dispose();
  }
}

void main() async {
  print('🚀 测试AI陪伴编排器修复');
  print('================================');

  final orchestrator = AiCompanionOrchestrator.instance;
  final testStt = TestSttService();
  
  try {
    print('📋 步骤1: 初始化编排器...');
    final success = await orchestrator.initialize(
      sttService: testStt,
    );
    
    if (success) {
      print('✅ 编排器初始化成功');
    } else {
      print('❌ 编排器初始化失败');
      return;
    }

    print('📋 步骤2: 启动AI陪伴模式...');
    final startSuccess = await orchestrator.start();
    
    if (startSuccess) {
      print('✅ AI陪伴模式启动成功');
      print('当前状态: ${orchestrator.currentState}');
    } else {
      print('❌ AI陪伴模式启动失败');
      print('错误信息: ${orchestrator.lastError}');
      return;
    }

    print('📋 步骤3: 模拟用户输入...');
    testStt.simulateResult('我今天很累', isFinal: true);
    
    // 等待一下让事件处理
    await Future.delayed(Duration(seconds: 2));
    
    print('📋 步骤4: 停止AI陪伴模式...');
    await orchestrator.stop();
    print('✅ AI陪伴模式已停止');

    print('\n🎉 测试完成！修复成功！');

  } catch (e, stackTrace) {
    print('❌ 测试失败: $e');
    print('堆栈跟踪: $stackTrace');
  } finally {
    await orchestrator.stop();
    testStt.dispose();
    AiCompanionOrchestrator.resetInstance();
  }
}
