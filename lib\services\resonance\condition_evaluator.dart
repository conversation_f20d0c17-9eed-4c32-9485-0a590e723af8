/// 条件评估器
/// 负责分析对话上下文并检测规则条件是否满足

import 'dart:math';
import 'package:flutter/foundation.dart';
import 'resonance_models.dart';
import 'resonance_engine_service.dart';
import 'ai_emotion_analyzer.dart';

/// 条件评估结果
class ConditionEvaluationResult {
  final bool isTriggered;
  final double confidence;
  final Map<String, dynamic> metadata;

  const ConditionEvaluationResult({
    required this.isTriggered,
    this.confidence = 1.0,
    this.metadata = const {},
  });

  factory ConditionEvaluationResult.triggered({
    double confidence = 1.0,
    Map<String, dynamic> metadata = const {},
  }) {
    return ConditionEvaluationResult(
      isTriggered: true,
      confidence: confidence,
      metadata: metadata,
    );
  }

  factory ConditionEvaluationResult.notTriggered() {
    return const ConditionEvaluationResult(isTriggered: false);
  }
}

/// 规则触发结果
class RuleTriggeredResult {
  final ResonanceRule rule;
  final ConditionEvaluationResult evaluationResult;
  final DateTime timestamp;

  const RuleTriggeredResult({
    required this.rule,
    required this.evaluationResult,
    required this.timestamp,
  });
}

/// 条件评估器
class ConditionEvaluator {
  /// 评估所有规则条件
  static Future<List<RuleTriggeredResult>> evaluateRules(
    List<ResonanceRule> rules,
    List<ConversationContextItem> context,
  ) async {
    final results = <RuleTriggeredResult>[];
    final now = DateTime.now();

    for (final rule in rules) {
      if (!rule.enabled) continue;

      final evaluationResult = await _evaluateCondition(rule.condition, context);

      if (evaluationResult.isTriggered) {
        results.add(RuleTriggeredResult(
          rule: rule,
          evaluationResult: evaluationResult,
          timestamp: now,
        ));
      }
    }

    // 按优先级排序，高优先级在前
    results.sort((a, b) => b.rule.priority.compareTo(a.rule.priority));

    return results;
  }

  /// 评估单个条件
  static Future<ConditionEvaluationResult> _evaluateCondition(
    ResonanceCondition condition,
    List<ConversationContextItem> context,
  ) async {
    switch (condition.type) {
      case ResonanceConditionType.keyword:
        return await _evaluateKeywordCondition(condition as KeywordCondition, context);
      case ResonanceConditionType.pause:
        return _evaluatePauseCondition(condition as PauseCondition, context);
      case ResonanceConditionType.emotion:
        return await _evaluateEmotionCondition(condition as EmotionCondition, context);
      case ResonanceConditionType.speechRate:
        return _evaluateSpeechRateCondition(condition as SpeechRateCondition, context);
    }
  }

  /// 评估关键词条件
  static Future<ConditionEvaluationResult> _evaluateKeywordCondition(
    KeywordCondition condition,
    List<ConversationContextItem> context,
  ) async {
    if (context.isEmpty) {
      return ConditionEvaluationResult.notTriggered();
    }

    // 检查最近的文本内容
    final recentTexts = context
        .where((item) => item.isFinal)
        .map((item) => item.text)
        .toList();

    if (recentTexts.isEmpty) {
      return ConditionEvaluationResult.notTriggered();
    }

    // 合并最近的文本
    final combinedText = recentTexts.join(' ');

    // 首先尝试传统关键词匹配（快速路径）
    final traditionalResult = _evaluateKeywordTraditional(condition, combinedText);
    if (traditionalResult.isTriggered) {
      return traditionalResult;
    }

    // 如果传统匹配失败，尝试AI语义分析（更准确但较慢）
    try {
      final aiResult = await AiEmotionAnalyzer.analyzeSemanticMatch(
        combinedText,
        condition.keywords,
      );

      if (aiResult != null && aiResult.isMatch && aiResult.confidence >= 0.6) {
        debugPrint('AI semantic condition triggered: "${aiResult.matchedConcepts.join(', ')}" in "$combinedText"');
        return ConditionEvaluationResult.triggered(
          confidence: aiResult.confidence,
          metadata: {
            'matchedConcepts': aiResult.matchedConcepts,
            'matchedText': combinedText,
            'reasoning': aiResult.reasoning,
            'analysisType': 'ai_semantic',
          },
        );
      }
    } catch (e) {
      debugPrint('AI semantic analysis failed, using traditional matching: $e');
    }

    return ConditionEvaluationResult.notTriggered();
  }

  /// 传统关键词匹配
  static ConditionEvaluationResult _evaluateKeywordTraditional(
    KeywordCondition condition,
    String combinedText,
  ) {
    final searchText = condition.caseSensitive ? combinedText : combinedText.toLowerCase();

    for (final keyword in condition.keywords) {
      final searchKeyword = condition.caseSensitive ? keyword : keyword.toLowerCase();

      bool isMatch = false;
      if (condition.exactMatch) {
        // 精确匹配：作为独立词汇
        final pattern = RegExp(r'\b' + RegExp.escape(searchKeyword) + r'\b');
        isMatch = pattern.hasMatch(searchText);
      } else {
        // 模糊匹配：包含即可
        isMatch = searchText.contains(searchKeyword);
      }

      if (isMatch) {
        debugPrint('Traditional keyword condition triggered: "$keyword" found in "$combinedText"');
        return ConditionEvaluationResult.triggered(
          confidence: 0.9,
          metadata: {
            'matchedKeyword': keyword,
            'matchedText': combinedText,
            'exactMatch': condition.exactMatch,
            'analysisType': 'traditional',
          },
        );
      }
    }

    return ConditionEvaluationResult.notTriggered();
  }

  /// 评估停顿条件
  static ConditionEvaluationResult _evaluatePauseCondition(
    PauseCondition condition,
    List<ConversationContextItem> context,
  ) {
    if (context.length < 2) {
      return ConditionEvaluationResult.notTriggered();
    }

    // 获取最后两个最终结果的时间戳
    final finalItems = context.where((item) => item.isFinal).toList();
    if (finalItems.length < 2) {
      return ConditionEvaluationResult.notTriggered();
    }

    final lastItem = finalItems.last;
    final secondLastItem = finalItems[finalItems.length - 2];

    final pauseDuration = lastItem.timestamp.difference(secondLastItem.timestamp);

    if (pauseDuration >= condition.minDuration && pauseDuration <= condition.maxDuration) {
      debugPrint('Pause condition triggered: ${pauseDuration.inMilliseconds}ms pause detected');
      return ConditionEvaluationResult.triggered(
        confidence: 0.8,
        metadata: {
          'pauseDuration': pauseDuration.inMilliseconds,
          'minDuration': condition.minDuration.inMilliseconds,
          'maxDuration': condition.maxDuration.inMilliseconds,
        },
      );
    }

    return ConditionEvaluationResult.notTriggered();
  }

  /// 评估情绪条件
  static Future<ConditionEvaluationResult> _evaluateEmotionCondition(
    EmotionCondition condition,
    List<ConversationContextItem> context,
  ) async {
    if (context.isEmpty) {
      return ConditionEvaluationResult.notTriggered();
    }

    // 获取最近的文本
    final recentTexts = context
        .where((item) => item.isFinal)
        .map((item) => item.text)
        .take(3) // 只分析最近3句话
        .toList();

    if (recentTexts.isEmpty) {
      return ConditionEvaluationResult.notTriggered();
    }

    final combinedText = recentTexts.join(' ');

    // 首先尝试AI情绪分析（更准确）
    try {
      final aiResult = await AiEmotionAnalyzer.analyzeEmotion(
        combinedText,
        condition.tendency,
      );

      if (aiResult != null &&
          aiResult.tendency == condition.tendency &&
          aiResult.confidence >= condition.threshold) {
        debugPrint('AI emotion condition triggered: ${condition.tendency.name} score ${aiResult.confidence}');
        return ConditionEvaluationResult.triggered(
          confidence: aiResult.confidence,
          metadata: {
            'emotionTendency': condition.tendency.name,
            'emotionScore': aiResult.confidence,
            'threshold': condition.threshold,
            'analyzedText': combinedText,
            'reasoning': aiResult.reasoning,
            'detectedEmotions': aiResult.detectedEmotions,
            'analysisType': 'ai_emotion',
          },
        );
      }
    } catch (e) {
      debugPrint('AI emotion analysis failed, using traditional analysis: $e');
    }

    // 如果AI分析失败，回退到传统关键词分析
    final emotionScore = _analyzeEmotion(combinedText, condition.tendency);

    if (emotionScore >= condition.threshold) {
      debugPrint('Traditional emotion condition triggered: ${condition.tendency.name} score $emotionScore');
      return ConditionEvaluationResult.triggered(
        confidence: emotionScore,
        metadata: {
          'emotionTendency': condition.tendency.name,
          'emotionScore': emotionScore,
          'threshold': condition.threshold,
          'analyzedText': combinedText,
          'analysisType': 'traditional',
        },
      );
    }

    return ConditionEvaluationResult.notTriggered();
  }

  /// 评估语速条件
  static ConditionEvaluationResult _evaluateSpeechRateCondition(
    SpeechRateCondition condition,
    List<ConversationContextItem> context,
  ) {
    if (context.length < 2) {
      return ConditionEvaluationResult.notTriggered();
    }

    // 计算最近几个文本片段的语速
    final finalItems = context.where((item) => item.isFinal).toList();
    if (finalItems.length < 2) {
      return ConditionEvaluationResult.notTriggered();
    }

    // 取最近的两个片段计算语速
    final recentItems = finalItems.take(2).toList();
    final totalWords = recentItems.map((item) => _countWords(item.text)).reduce((a, b) => a + b);
    final totalDuration = recentItems.last.timestamp.difference(recentItems.first.timestamp);

    if (totalDuration.inMilliseconds == 0) {
      return ConditionEvaluationResult.notTriggered();
    }

    final wordsPerSecond = totalWords / (totalDuration.inMilliseconds / 1000.0);

    if (wordsPerSecond >= condition.minWordsPerSecond && 
        wordsPerSecond <= condition.maxWordsPerSecond) {
      debugPrint('Speech rate condition triggered: ${wordsPerSecond.toStringAsFixed(2)} words/sec');
      return ConditionEvaluationResult.triggered(
        confidence: 0.7,
        metadata: {
          'wordsPerSecond': wordsPerSecond,
          'minWordsPerSecond': condition.minWordsPerSecond,
          'maxWordsPerSecond': condition.maxWordsPerSecond,
          'totalWords': totalWords,
          'durationMs': totalDuration.inMilliseconds,
        },
      );
    }

    return ConditionEvaluationResult.notTriggered();
  }

  /// 简单的情绪分析
  static double _analyzeEmotion(String text, EmotionTendency tendency) {
    // 简单的基于关键词的情绪分析
    final negativeKeywords = [
      '累', '烦', '难过', '伤心', '痛苦', '委屈', '生气', '愤怒', '沮丧', '失望',
      '焦虑', '担心', '害怕', '恐惧', '绝望', '无助', '孤独', '寂寞', '压抑',
      '不开心', '不高兴', '难受', '痛', '苦', '哭', '眼泪', '崩溃', '受不了'
    ];

    final positiveKeywords = [
      '开心', '高兴', '快乐', '兴奋', '激动', '满足', '幸福', '愉快', '舒服',
      '放松', '轻松', '安心', '感谢', '感激', '喜欢', '爱', '美好', '棒', '好'
    ];

    final lowerText = text.toLowerCase();
    int negativeCount = 0;
    int positiveCount = 0;

    for (final keyword in negativeKeywords) {
      if (lowerText.contains(keyword)) {
        negativeCount++;
      }
    }

    for (final keyword in positiveKeywords) {
      if (lowerText.contains(keyword)) {
        positiveCount++;
      }
    }

    switch (tendency) {
      case EmotionTendency.negative:
        if (negativeCount == 0) return 0.0;
        return min(1.0, negativeCount / max(1, negativeCount + positiveCount));
      
      case EmotionTendency.positive:
        if (positiveCount == 0) return 0.0;
        return min(1.0, positiveCount / max(1, negativeCount + positiveCount));
      
      case EmotionTendency.neutral:
        if (negativeCount == 0 && positiveCount == 0) return 1.0;
        return max(0.0, 1.0 - (negativeCount + positiveCount) / text.length * 10);
    }
  }

  /// 计算文本中的词汇数量
  static int _countWords(String text) {
    if (text.trim().isEmpty) return 0;
    
    // 简单的中文词汇计数：按字符计算，因为中文通常一个字符就是一个词
    // 对于英文，按空格分割
    final chineseCharCount = text.runes.where((rune) {
      return rune >= 0x4e00 && rune <= 0x9fff; // 中文字符范围
    }).length;
    
    final englishWords = text.split(RegExp(r'\s+')).where((word) {
      return word.isNotEmpty && RegExp(r'^[a-zA-Z]+$').hasMatch(word);
    }).length;
    
    return chineseCharCount + englishWords;
  }
}
