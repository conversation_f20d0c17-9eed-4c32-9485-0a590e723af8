import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'stt_models.dart';

/// STT错误恢复管理器
/// 
/// 提供智能的错误处理、重连策略和服务健康监控
class SttErrorRecoveryManager extends ChangeNotifier {
  // 配置参数
  final int maxRetryAttempts;
  final Duration baseRetryDelay;
  final Duration maxRetryDelay;
  final Duration healthCheckInterval;
  final double backoffMultiplier;
  
  // 内部状态
  int _currentRetryAttempts = 0;
  DateTime? _lastErrorTime;
  DateTime? _lastSuccessTime;
  Timer? _healthCheckTimer;
  Timer? _retryTimer;
  
  // 错误统计
  final Map<SttErrorType, int> _errorCounts = {};
  final List<SttError> _recentErrors = [];
  static const int _maxRecentErrors = 20;
  
  // 健康状态
  SttServiceHealth _currentHealth = SttServiceHealth.unknown;
  double _successRate = 1.0;
  int _totalOperations = 0;
  int _successfulOperations = 0;
  
  // 事件流
  final StreamController<SttServiceHealth> _healthController = StreamController<SttServiceHealth>.broadcast();
  final StreamController<SttRecoveryAction> _recoveryController = StreamController<SttRecoveryAction>.broadcast();
  final StreamController<SttErrorRecoveryStats> _statsController = StreamController<SttErrorRecoveryStats>.broadcast();

  SttErrorRecoveryManager({
    this.maxRetryAttempts = 5,
    this.baseRetryDelay = const Duration(seconds: 1),
    this.maxRetryDelay = const Duration(seconds: 30),
    this.healthCheckInterval = const Duration(seconds: 10),
    this.backoffMultiplier = 2.0,
  });

  /// 事件流
  Stream<SttServiceHealth> get onHealthChanged => _healthController.stream;
  Stream<SttRecoveryAction> get onRecoveryAction => _recoveryController.stream;
  Stream<SttErrorRecoveryStats> get onStatsUpdate => _statsController.stream;
  
  /// 当前状态
  SttServiceHealth get currentHealth => _currentHealth;
  int get currentRetryAttempts => _currentRetryAttempts;
  double get successRate => _successRate;
  bool get isRetrying => _retryTimer != null;
  
  /// 统计信息
  SttErrorRecoveryStats get stats => SttErrorRecoveryStats(
    totalOperations: _totalOperations,
    successfulOperations: _successfulOperations,
    successRate: _successRate,
    currentRetryAttempts: _currentRetryAttempts,
    maxRetryAttempts: maxRetryAttempts,
    currentHealth: _currentHealth,
    errorCounts: Map.from(_errorCounts),
    recentErrors: List.from(_recentErrors),
    lastErrorTime: _lastErrorTime,
    lastSuccessTime: _lastSuccessTime,
  );

  /// 开始健康监控
  void startHealthMonitoring() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = Timer.periodic(healthCheckInterval, (_) {
      _performHealthCheck();
    });
    
    _updateHealth(SttServiceHealth.monitoring);
    debugPrint('STT错误恢复管理器开始健康监控');
  }

  /// 停止健康监控
  void stopHealthMonitoring() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = null;
    
    _updateHealth(SttServiceHealth.unknown);
    debugPrint('STT错误恢复管理器停止健康监控');
  }

  /// 记录操作成功
  void recordSuccess() {
    _totalOperations++;
    _successfulOperations++;
    _lastSuccessTime = DateTime.now();
    _currentRetryAttempts = 0; // 重置重试计数
    
    _updateSuccessRate();
    _updateHealthBasedOnStats();
    _emitStats();
    
    debugPrint('STT操作成功 - 成功率: ${(_successRate * 100).toStringAsFixed(1)}%');
  }

  /// 记录错误并决定恢复策略
  Future<SttRecoveryAction> recordError(SttError error) async {
    _totalOperations++;
    _lastErrorTime = DateTime.now();
    
    // 更新错误统计
    _errorCounts[error.type] = (_errorCounts[error.type] ?? 0) + 1;
    _recentErrors.add(error);
    
    // 限制最近错误列表大小
    while (_recentErrors.length > _maxRecentErrors) {
      _recentErrors.removeAt(0);
    }
    
    _updateSuccessRate();
    _updateHealthBasedOnStats();
    
    // 决定恢复策略
    final action = _determineRecoveryAction(error);
    _recoveryController.add(action);
    _emitStats();
    
    debugPrint('STT错误记录: ${error.type} - ${error.message}, 恢复策略: ${action.type}');
    
    return action;
  }

  /// 执行重试
  Future<void> executeRetry(Future<void> Function() retryFunction) async {
    if (_currentRetryAttempts >= maxRetryAttempts) {
      debugPrint('已达最大重试次数，停止重试');
      _updateHealth(SttServiceHealth.failed);
      return;
    }

    _currentRetryAttempts++;
    final delay = _calculateRetryDelay();
    
    debugPrint('执行第 $_currentRetryAttempts 次重试，延迟: ${delay.inSeconds} 秒');
    _updateHealth(SttServiceHealth.retrying);
    
    _retryTimer = Timer(delay, () async {
      _retryTimer = null;
      
      try {
        await retryFunction();
        recordSuccess();
        debugPrint('重试成功！');
      } catch (e) {
        debugPrint('重试失败: $e');
        
        // 创建重试失败错误
        final retryError = SttError(
          type: SttErrorType.unknown,
          code: 'RETRY_FAILED',
          message: '重试失败: $e',
          timestamp: DateTime.now(),
          details: e,
        );
        
        await recordError(retryError);
      }
    });
  }

  /// 重置状态
  void reset() {
    _currentRetryAttempts = 0;
    _lastErrorTime = null;
    _lastSuccessTime = null;
    _errorCounts.clear();
    _recentErrors.clear();
    _totalOperations = 0;
    _successfulOperations = 0;
    _successRate = 1.0;
    
    _retryTimer?.cancel();
    _retryTimer = null;
    
    _updateHealth(SttServiceHealth.healthy);
    _emitStats();
    
    debugPrint('STT错误恢复管理器状态已重置');
  }

  /// 获取错误分析报告
  Map<String, dynamic> getErrorAnalysis() {
    final totalErrors = _errorCounts.values.fold(0, (sum, count) => sum + count);
    
    return {
      'totalErrors': totalErrors,
      'errorBreakdown': _errorCounts.map((type, count) => 
          MapEntry(type.toString().split('.').last, count)),
      'mostCommonError': _getMostCommonError(),
      'errorRate': totalErrors / (_totalOperations > 0 ? _totalOperations : 1),
      'recentErrorTrend': _getRecentErrorTrend(),
      'recommendations': _getRecommendations(),
    };
  }

  /// 确定恢复策略
  SttRecoveryAction _determineRecoveryAction(SttError error) {
    // 基于错误类型确定策略
    switch (error.type) {
      case SttErrorType.network:
        if (_currentRetryAttempts < maxRetryAttempts) {
          return SttRecoveryAction(
            type: SttRecoveryActionType.retry,
            delay: _calculateRetryDelay(),
            reason: '网络错误，尝试重连',
          );
        } else {
          return SttRecoveryAction(
            type: SttRecoveryActionType.fail,
            reason: '网络错误重试次数已达上限',
          );
        }
        
      case SttErrorType.authentication:
        return SttRecoveryAction(
          type: SttRecoveryActionType.fail,
          reason: '认证错误，需要检查配置',
        );
        
      case SttErrorType.audioFormat:
        if (_currentRetryAttempts < 2) { // 音频格式错误重试次数较少
          return SttRecoveryAction(
            type: SttRecoveryActionType.retry,
            delay: const Duration(seconds: 1),
            reason: '音频格式错误，尝试重新初始化',
          );
        } else {
          return SttRecoveryAction(
            type: SttRecoveryActionType.fail,
            reason: '音频格式错误，需要检查音频配置',
          );
        }
        
      case SttErrorType.rateLimit:
        return SttRecoveryAction(
          type: SttRecoveryActionType.retry,
          delay: const Duration(seconds: 10), // 限流错误需要较长延迟
          reason: '触发限流，等待后重试',
        );
        
      case SttErrorType.unknown:
      default:
        if (_currentRetryAttempts < maxRetryAttempts ~/ 2) {
          return SttRecoveryAction(
            type: SttRecoveryActionType.retry,
            delay: _calculateRetryDelay(),
            reason: '未知错误，尝试重试',
          );
        } else {
          return SttRecoveryAction(
            type: SttRecoveryActionType.fail,
            reason: '未知错误重试次数已达上限',
          );
        }
    }
  }

  /// 计算重试延迟（指数退避）
  Duration _calculateRetryDelay() {
    final delay = baseRetryDelay * pow(backoffMultiplier, _currentRetryAttempts - 1);
    final delayMs = delay.inMilliseconds.clamp(
      baseRetryDelay.inMilliseconds,
      maxRetryDelay.inMilliseconds,
    );
    return Duration(milliseconds: delayMs);
  }

  /// 执行健康检查
  void _performHealthCheck() {
    final now = DateTime.now();
    
    // 检查最近是否有成功操作
    if (_lastSuccessTime != null) {
      final timeSinceSuccess = now.difference(_lastSuccessTime!);
      if (timeSinceSuccess > const Duration(minutes: 5)) {
        _updateHealth(SttServiceHealth.degraded);
        return;
      }
    }
    
    // 检查最近是否有错误
    if (_lastErrorTime != null) {
      final timeSinceError = now.difference(_lastErrorTime!);
      if (timeSinceError < const Duration(minutes: 1)) {
        _updateHealth(SttServiceHealth.degraded);
        return;
      }
    }
    
    // 基于成功率判断健康状态
    _updateHealthBasedOnStats();
  }

  /// 基于统计数据更新健康状态
  void _updateHealthBasedOnStats() {
    if (_successRate >= 0.9) {
      _updateHealth(SttServiceHealth.healthy);
    } else if (_successRate >= 0.7) {
      _updateHealth(SttServiceHealth.degraded);
    } else {
      _updateHealth(SttServiceHealth.unhealthy);
    }
  }

  /// 更新健康状态
  void _updateHealth(SttServiceHealth newHealth) {
    if (_currentHealth != newHealth) {
      _currentHealth = newHealth;
      _healthController.add(newHealth);
      notifyListeners();
      debugPrint('STT服务健康状态变更: $newHealth');
    }
  }

  /// 更新成功率
  void _updateSuccessRate() {
    if (_totalOperations > 0) {
      _successRate = _successfulOperations / _totalOperations;
    } else {
      _successRate = 1.0;
    }
  }

  /// 获取最常见错误
  String? _getMostCommonError() {
    if (_errorCounts.isEmpty) return null;
    
    var maxCount = 0;
    SttErrorType? mostCommon;
    
    _errorCounts.forEach((type, count) {
      if (count > maxCount) {
        maxCount = count;
        mostCommon = type;
      }
    });
    
    return mostCommon?.toString().split('.').last;
  }

  /// 获取最近错误趋势
  String _getRecentErrorTrend() {
    if (_recentErrors.length < 2) return 'insufficient_data';
    
    final recentCount = _recentErrors.where((error) =>
        DateTime.now().difference(error.timestamp) < const Duration(minutes: 5)
    ).length;
    
    if (recentCount == 0) return 'improving';
    if (recentCount >= _recentErrors.length * 0.5) return 'worsening';
    return 'stable';
  }

  /// 获取建议
  List<String> _getRecommendations() {
    final recommendations = <String>[];
    
    if (_successRate < 0.8) {
      recommendations.add('成功率较低，建议检查网络连接和服务配置');
    }
    
    if (_errorCounts[SttErrorType.network] != null && 
        _errorCounts[SttErrorType.network]! > 3) {
      recommendations.add('网络错误频繁，建议检查网络稳定性');
    }
    
    if (_errorCounts[SttErrorType.authentication] != null) {
      recommendations.add('存在认证错误，建议检查API密钥配置');
    }
    
    if (_currentRetryAttempts >= maxRetryAttempts * 0.8) {
      recommendations.add('重试次数接近上限，建议检查服务状态');
    }
    
    return recommendations;
  }

  /// 发送统计信息
  void _emitStats() {
    _statsController.add(stats);
  }

  @override
  void dispose() {
    _healthCheckTimer?.cancel();
    _retryTimer?.cancel();
    _healthController.close();
    _recoveryController.close();
    _statsController.close();
    super.dispose();
  }
}

/// STT服务健康状态
enum SttServiceHealth {
  unknown,
  healthy,
  degraded,
  unhealthy,
  retrying,
  failed,
  monitoring,
}

/// STT恢复动作类型
enum SttRecoveryActionType {
  retry,
  fail,
  reset,
  wait,
}

/// STT恢复动作
@immutable
class SttRecoveryAction {
  final SttRecoveryActionType type;
  final Duration? delay;
  final String reason;

  const SttRecoveryAction({
    required this.type,
    this.delay,
    required this.reason,
  });

  @override
  String toString() {
    return 'SttRecoveryAction(type: $type, delay: $delay, reason: $reason)';
  }
}

/// STT错误恢复统计信息
@immutable
class SttErrorRecoveryStats {
  final int totalOperations;
  final int successfulOperations;
  final double successRate;
  final int currentRetryAttempts;
  final int maxRetryAttempts;
  final SttServiceHealth currentHealth;
  final Map<SttErrorType, int> errorCounts;
  final List<SttError> recentErrors;
  final DateTime? lastErrorTime;
  final DateTime? lastSuccessTime;

  const SttErrorRecoveryStats({
    required this.totalOperations,
    required this.successfulOperations,
    required this.successRate,
    required this.currentRetryAttempts,
    required this.maxRetryAttempts,
    required this.currentHealth,
    required this.errorCounts,
    required this.recentErrors,
    this.lastErrorTime,
    this.lastSuccessTime,
  });

  @override
  String toString() {
    return 'SttErrorRecoveryStats(operations: $totalOperations, success: ${(successRate * 100).toStringAsFixed(1)}%, health: $currentHealth, retries: $currentRetryAttempts/$maxRetryAttempts)';
  }
}
