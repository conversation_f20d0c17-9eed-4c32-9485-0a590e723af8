/// AI情绪分析器
/// 使用DeepSeek大模型进行更准确的情绪分析

import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import '../config_service.dart';
import 'resonance_models.dart';

/// AI情绪分析结果
class AiEmotionResult {
  final EmotionTendency tendency;
  final double confidence;
  final String reasoning;
  final List<String> detectedEmotions;
  final Map<String, dynamic> metadata;

  const AiEmotionResult({
    required this.tendency,
    required this.confidence,
    required this.reasoning,
    required this.detectedEmotions,
    this.metadata = const {},
  });

  factory AiEmotionResult.fromJson(Map<String, dynamic> json) {
    return AiEmotionResult(
      tendency: EmotionTendency.values.firstWhere(
        (e) => e.name == json['tendency'],
        orElse: () => EmotionTendency.neutral,
      ),
      confidence: (json['confidence'] as num).toDouble(),
      reasoning: json['reasoning'] ?? '',
      detectedEmotions: List<String>.from(json['detectedEmotions'] ?? []),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }
}

/// AI语义分析结果
class AiSemanticResult {
  final bool isMatch;
  final double confidence;
  final String reasoning;
  final List<String> matchedConcepts;

  const AiSemanticResult({
    required this.isMatch,
    required this.confidence,
    required this.reasoning,
    required this.matchedConcepts,
  });

  factory AiSemanticResult.fromJson(Map<String, dynamic> json) {
    return AiSemanticResult(
      isMatch: json['isMatch'] ?? false,
      confidence: (json['confidence'] as num).toDouble(),
      reasoning: json['reasoning'] ?? '',
      matchedConcepts: List<String>.from(json['matchedConcepts'] ?? []),
    );
  }
}

/// AI情绪分析器
class AiEmotionAnalyzer {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();
  static const String _apiKeyName = 'deepseek_api_key';

  // 缓存机制
  static final Map<String, AiEmotionResult> _emotionCache = {};
  static final Map<String, AiSemanticResult> _semanticCache = {};
  static const int _maxCacheSize = 100;

  /// 获取API URL
  static String get _apiUrl {
    final baseUrl = ConfigService.instance.deepseekBaseUrl;
    return '$baseUrl/v1/chat/completions';
  }

  /// 使用AI分析文本情绪
  static Future<AiEmotionResult?> analyzeEmotion(
    String text,
    EmotionTendency targetTendency,
  ) async {
    if (text.trim().isEmpty) return null;

    // 检查缓存
    final cacheKey = '${text}_${targetTendency.name}';
    if (_emotionCache.containsKey(cacheKey)) {
      return _emotionCache[cacheKey];
    }

    try {
      final apiKey = await _secureStorage.read(key: _apiKeyName);
      if (apiKey == null || apiKey.isEmpty) {
        debugPrint('DeepSeek API key not found, falling back to keyword analysis');
        return null;
      }

      final systemPrompt = _buildEmotionAnalysisPrompt(targetTendency);
      
      final response = await http.post(
        Uri.parse(_apiUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: jsonEncode({
          'model': 'deepseek-chat',
          'messages': [
            {'role': 'system', 'content': systemPrompt},
            {'role': 'user', 'content': text},
          ],
          'temperature': 0.3, // 较低温度确保一致性
          'max_tokens': 200,
          'top_p': 0.9,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        final content = data['choices'][0]['message']['content'] as String;
        
        try {
          final jsonResult = jsonDecode(content);
          final result = AiEmotionResult.fromJson(jsonResult);
          
          // 缓存结果
          _cacheEmotionResult(cacheKey, result);
          
          debugPrint('AI emotion analysis: ${result.tendency.name} (${result.confidence})');
          return result;
        } catch (e) {
          debugPrint('Failed to parse AI emotion response: $e');
          return null;
        }
      } else {
        debugPrint('AI emotion analysis API error: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('AI emotion analysis error: $e');
      return null;
    }
  }

  /// 使用AI进行语义匹配分析
  static Future<AiSemanticResult?> analyzeSemanticMatch(
    String text,
    List<String> targetConcepts,
  ) async {
    if (text.trim().isEmpty || targetConcepts.isEmpty) return null;

    // 检查缓存
    final cacheKey = '${text}_${targetConcepts.join(',')}';
    if (_semanticCache.containsKey(cacheKey)) {
      return _semanticCache[cacheKey];
    }

    try {
      final apiKey = await _secureStorage.read(key: _apiKeyName);
      if (apiKey == null || apiKey.isEmpty) {
        debugPrint('DeepSeek API key not found, falling back to keyword matching');
        return null;
      }

      final systemPrompt = _buildSemanticAnalysisPrompt(targetConcepts);
      
      final response = await http.post(
        Uri.parse(_apiUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: jsonEncode({
          'model': 'deepseek-chat',
          'messages': [
            {'role': 'system', 'content': systemPrompt},
            {'role': 'user', 'content': text},
          ],
          'temperature': 0.2, // 更低温度确保准确性
          'max_tokens': 150,
          'top_p': 0.8,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        final content = data['choices'][0]['message']['content'] as String;
        
        try {
          final jsonResult = jsonDecode(content);
          final result = AiSemanticResult.fromJson(jsonResult);
          
          // 缓存结果
          _cacheSemanticResult(cacheKey, result);
          
          debugPrint('AI semantic analysis: ${result.isMatch} (${result.confidence})');
          return result;
        } catch (e) {
          debugPrint('Failed to parse AI semantic response: $e');
          return null;
        }
      } else {
        debugPrint('AI semantic analysis API error: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('AI semantic analysis error: $e');
      return null;
    }
  }

  /// 构建情绪分析提示词
  static String _buildEmotionAnalysisPrompt(EmotionTendency targetTendency) {
    return '''
你是一个专业的情绪分析专家。请分析用户文本的情绪倾向，特别关注是否符合目标情绪类型：${_getTendencyDescription(targetTendency)}。

请以JSON格式返回分析结果：
{
  "tendency": "positive|negative|neutral",
  "confidence": 0.0-1.0,
  "reasoning": "分析理由",
  "detectedEmotions": ["具体检测到的情绪词汇"],
  "metadata": {
    "intensity": "low|medium|high",
    "context": "上下文信息"
  }
}

分析要求：
1. 理解文本的深层含义，不仅仅是表面词汇
2. 考虑中文的语境和文化背景
3. 识别隐含的情绪表达
4. confidence应该反映分析的确定程度
5. 如果文本情绪不明确，tendency设为"neutral"，confidence设为较低值

请只返回JSON，不要包含其他内容。
''';
  }

  /// 构建语义分析提示词
  static String _buildSemanticAnalysisPrompt(List<String> targetConcepts) {
    return '''
你是一个专业的语义分析专家。请分析用户文本是否在语义上匹配以下概念：${targetConcepts.join('、')}。

请以JSON格式返回分析结果：
{
  "isMatch": true|false,
  "confidence": 0.0-1.0,
  "reasoning": "匹配理由",
  "matchedConcepts": ["匹配到的概念"]
}

分析要求：
1. 进行语义理解，不仅仅是字面匹配
2. 识别同义词、近义词和相关概念
3. 考虑上下文和语境
4. confidence应该反映匹配的确定程度
5. 如果有任何一个概念匹配，isMatch设为true

请只返回JSON，不要包含其他内容。
''';
  }

  /// 获取情绪倾向描述
  static String _getTendencyDescription(EmotionTendency tendency) {
    switch (tendency) {
      case EmotionTendency.positive:
        return '积极正面情绪（开心、满足、兴奋、感激等）';
      case EmotionTendency.negative:
        return '消极负面情绪（悲伤、愤怒、焦虑、失望、疲惫等）';
      case EmotionTendency.neutral:
        return '中性平和情绪（平静、客观、无明显情绪倾向）';
    }
  }

  /// 缓存情绪分析结果
  static void _cacheEmotionResult(String key, AiEmotionResult result) {
    if (_emotionCache.length >= _maxCacheSize) {
      final firstKey = _emotionCache.keys.first;
      _emotionCache.remove(firstKey);
    }
    _emotionCache[key] = result;
  }

  /// 缓存语义分析结果
  static void _cacheSemanticResult(String key, AiSemanticResult result) {
    if (_semanticCache.length >= _maxCacheSize) {
      final firstKey = _semanticCache.keys.first;
      _semanticCache.remove(firstKey);
    }
    _semanticCache[key] = result;
  }

  /// 清除缓存
  static void clearCache() {
    _emotionCache.clear();
    _semanticCache.clear();
  }

  /// 获取缓存统计
  static Map<String, int> getCacheStats() {
    return {
      'emotionCacheSize': _emotionCache.length,
      'semanticCacheSize': _semanticCache.length,
    };
  }
}
