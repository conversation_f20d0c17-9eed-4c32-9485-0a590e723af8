/// 共鸣引擎数据模型单元测试

import 'package:flutter_test/flutter_test.dart';
import 'package:echo_cave/services/resonance/resonance_models.dart';
import 'package:echo_cave/services/resonance/resonance_rule_loader.dart';

void main() {
  group('ResonanceModels Tests', () {
    group('KeywordCondition', () {
      test('should create from JSON correctly', () {
        final json = {
          'type': 'keyword',
          'parameters': {
            'keywords': ['test', 'example'],
            'caseSensitive': true,
            'exactMatch': false,
          },
        };

        final condition = KeywordCondition.fromJson(json);

        expect(condition.type, ResonanceConditionType.keyword);
        expect(condition.keywords, ['test', 'example']);
        expect(condition.caseSensitive, true);
        expect(condition.exactMatch, false);
      });

      test('should convert to JSON correctly', () {
        final condition = KeywordCondition(
          keywords: ['hello', 'world'],
          caseSensitive: false,
          exactMatch: true,
        );

        final json = condition.toJson();

        expect(json['type'], 'keyword');
        expect(json['parameters']['keywords'], ['hello', 'world']);
        expect(json['parameters']['caseSensitive'], false);
        expect(json['parameters']['exactMatch'], true);
      });
    });

    group('PauseCondition', () {
      test('should create from JSON correctly', () {
        final json = {
          'type': 'pause',
          'parameters': {
            'minDurationMs': 1500,
            'maxDurationMs': 5000,
          },
        };

        final condition = PauseCondition.fromJson(json);

        expect(condition.type, ResonanceConditionType.pause);
        expect(condition.minDuration, const Duration(milliseconds: 1500));
        expect(condition.maxDuration, const Duration(milliseconds: 5000));
      });

      test('should use default maxDuration if not provided', () {
        final json = {
          'type': 'pause',
          'parameters': {
            'minDurationMs': 1000,
          },
        };

        final condition = PauseCondition.fromJson(json);

        expect(condition.maxDuration, const Duration(milliseconds: 10000));
      });
    });

    group('EmotionCondition', () {
      test('should create from JSON correctly', () {
        final json = {
          'type': 'emotion',
          'parameters': {
            'tendency': 'negative',
            'threshold': 0.8,
          },
        };

        final condition = EmotionCondition.fromJson(json);

        expect(condition.type, ResonanceConditionType.emotion);
        expect(condition.tendency, EmotionTendency.negative);
        expect(condition.threshold, 0.8);
      });
    });

    group('ResonanceAction', () {
      test('should create from JSON correctly', () {
        final json = {
          'type': 'empathyResponse',
          'responsePool': ['I understand', 'I hear you'],
          'metadata': {'supportive': true},
        };

        final action = ResonanceAction.fromJson(json);

        expect(action.type, ResonanceActionType.empathyResponse);
        expect(action.responsePool, ['I understand', 'I hear you']);
        expect(action.metadata['supportive'], true);
      });

      test('should convert to JSON correctly', () {
        final action = ResonanceAction(
          type: ResonanceActionType.encouragement,
          responsePool: ['Keep going', 'You can do it'],
          metadata: {'motivational': true},
        );

        final json = action.toJson();

        expect(json['type'], 'encouragement');
        expect(json['responsePool'], ['Keep going', 'You can do it']);
        expect(json['metadata']['motivational'], true);
      });
    });

    group('ResonanceRule', () {
      test('should create from JSON correctly', () {
        final json = {
          'id': 'test_rule',
          'name': 'Test Rule',
          'description': 'A test rule',
          'condition': {
            'type': 'keyword',
            'parameters': {
              'keywords': ['test'],
              'caseSensitive': false,
              'exactMatch': false,
            },
          },
          'action': {
            'type': 'acknowledgment',
            'responsePool': ['OK'],
            'metadata': {},
          },
          'priority': 2,
          'cooldownSeconds': 15,
          'enabled': true,
        };

        final rule = ResonanceRule.fromJson(json);

        expect(rule.id, 'test_rule');
        expect(rule.name, 'Test Rule');
        expect(rule.description, 'A test rule');
        expect(rule.condition.type, ResonanceConditionType.keyword);
        expect(rule.action.type, ResonanceActionType.acknowledgment);
        expect(rule.priority, 2);
        expect(rule.cooldown, const Duration(seconds: 15));
        expect(rule.enabled, true);
      });

      test('should use default values when not provided', () {
        final json = {
          'id': 'minimal_rule',
          'name': 'Minimal Rule',
          'description': 'A minimal rule',
          'condition': {
            'type': 'pause',
            'parameters': {
              'minDurationMs': 1000,
            },
          },
          'action': {
            'type': 'acknowledgment',
            'responsePool': ['Hmm'],
          },
        };

        final rule = ResonanceRule.fromJson(json);

        expect(rule.priority, 1);
        expect(rule.cooldown, const Duration(seconds: 10));
        expect(rule.enabled, true);
      });
    });

    group('ResonanceRuleSet', () {
      test('should create from JSON correctly', () {
        final json = {
          'version': '1.0.0',
          'name': 'Test Rules',
          'description': 'Test rule set',
          'rules': [
            {
              'id': 'rule1',
              'name': 'Rule 1',
              'description': 'First rule',
              'condition': {
                'type': 'keyword',
                'parameters': {
                  'keywords': ['hello'],
                  'caseSensitive': false,
                  'exactMatch': false,
                },
              },
              'action': {
                'type': 'acknowledgment',
                'responsePool': ['Hi'],
              },
            },
          ],
        };

        final ruleSet = ResonanceRuleSet.fromJson(json);

        expect(ruleSet.version, '1.0.0');
        expect(ruleSet.name, 'Test Rules');
        expect(ruleSet.description, 'Test rule set');
        expect(ruleSet.rules.length, 1);
        expect(ruleSet.rules.first.id, 'rule1');
      });

      test('should convert to JSON correctly', () {
        final ruleSet = ResonanceRuleSet(
          version: '2.0.0',
          name: 'Sample Rules',
          description: 'Sample rule set',
          rules: [
            ResonanceRule(
              id: 'sample_rule',
              name: 'Sample Rule',
              description: 'A sample rule',
              condition: KeywordCondition(keywords: ['sample']),
              action: ResonanceAction(
                type: ResonanceActionType.acknowledgment,
                responsePool: ['Got it'],
              ),
            ),
          ],
        );

        final json = ruleSet.toJson();

        expect(json['version'], '2.0.0');
        expect(json['name'], 'Sample Rules');
        expect(json['description'], 'Sample rule set');
        expect(json['rules'], isA<List>());
        expect((json['rules'] as List).length, 1);
      });
    });

    group('ResonanceCondition Factory', () {
      test('should create correct condition type from JSON', () {
        final keywordJson = {
          'type': 'keyword',
          'parameters': {
            'keywords': ['test'],
            'caseSensitive': false,
            'exactMatch': false,
          },
        };

        final pauseJson = {
          'type': 'pause',
          'parameters': {
            'minDurationMs': 1000,
          },
        };

        final keywordCondition = ResonanceCondition.fromJson(keywordJson);
        final pauseCondition = ResonanceCondition.fromJson(pauseJson);

        expect(keywordCondition, isA<KeywordCondition>());
        expect(pauseCondition, isA<PauseCondition>());
      });
    });

    group('ResonanceRuleLoader', () {
      test('should load rules from valid JSON string', () {
        const jsonString = '''
        {
          "version": "1.0.0",
          "name": "Test Rules",
          "description": "Test rule set",
          "rules": [
            {
              "id": "test_keyword",
              "name": "Test Keyword Rule",
              "description": "A test keyword rule",
              "condition": {
                "type": "keyword",
                "parameters": {
                  "keywords": ["test"],
                  "caseSensitive": false,
                  "exactMatch": false
                }
              },
              "action": {
                "type": "acknowledgment",
                "responsePool": ["I understand"]
              }
            }
          ]
        }
        ''';

        final result = ResonanceRuleLoader.loadRulesFromJson(jsonString);

        expect(result.success, true);
        expect(result.ruleSet, isNotNull);
        expect(result.ruleSet!.rules.length, 1);
        expect(result.ruleSet!.rules.first.id, 'test_keyword');
      });

      test('should fail with invalid JSON', () {
        const invalidJson = '{ invalid json }';

        final result = ResonanceRuleLoader.loadRulesFromJson(invalidJson);

        expect(result.success, false);
        expect(result.errorMessage, contains('Failed to parse JSON'));
      });

      test('should validate rule set correctly', () {
        final validRuleSet = ResonanceRuleSet(
          version: '1.0.0',
          name: 'Valid Rules',
          description: 'A valid rule set',
          rules: [
            ResonanceRule(
              id: 'valid_rule',
              name: 'Valid Rule',
              description: 'A valid rule',
              condition: KeywordCondition(keywords: ['valid']),
              action: ResonanceAction(
                type: ResonanceActionType.acknowledgment,
                responsePool: ['OK'],
              ),
            ),
          ],
        );

        final result = ResonanceRuleLoader.validateRuleSet(validRuleSet);

        expect(result.isValid, true);
        expect(result.errors, isEmpty);
      });

      test('should detect duplicate rule IDs', () {
        final invalidRuleSet = ResonanceRuleSet(
          version: '1.0.0',
          name: 'Invalid Rules',
          description: 'A rule set with duplicate IDs',
          rules: [
            ResonanceRule(
              id: 'duplicate_id',
              name: 'Rule 1',
              description: 'First rule',
              condition: KeywordCondition(keywords: ['test1']),
              action: ResonanceAction(
                type: ResonanceActionType.acknowledgment,
                responsePool: ['OK1'],
              ),
            ),
            ResonanceRule(
              id: 'duplicate_id',
              name: 'Rule 2',
              description: 'Second rule',
              condition: KeywordCondition(keywords: ['test2']),
              action: ResonanceAction(
                type: ResonanceActionType.acknowledgment,
                responsePool: ['OK2'],
              ),
            ),
          ],
        );

        final result = ResonanceRuleLoader.validateRuleSet(invalidRuleSet);

        expect(result.isValid, false);
        expect(result.errors, contains('Duplicate rule ID: duplicate_id'));
      });
    });
  });
}
