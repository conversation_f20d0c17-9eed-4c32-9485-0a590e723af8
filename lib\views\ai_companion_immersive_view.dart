/// AI陪伴沉浸式界面
/// 
/// 使用Task 26的AI陪伴编排器提供完整的AI陪伴体验

import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../services/ai_companion_orchestrator.dart';
import '../services/parallel_audio_io_service.dart';
import '../services/stt/streaming_stt_service.dart';
import '../services/tts_service.dart';
import '../services/ambient_sound_service.dart';
import '../views/main_view.dart';
// 暂时注释掉不需要的组件
// import '../widgets/emotion_visualization.dart';
// import '../widgets/voice_waveform.dart';
// import '../widgets/immersive_effects.dart';

/// AI陪伴沉浸式界面
class AiCompanionImmersiveView extends HookConsumerWidget {
  const AiCompanionImmersiveView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 服务实例
    final orchestrator = useMemoized(() => AiCompanionOrchestrator.instance);
    final audioService = useMemoized(() => ParallelAudioIOService());
    // 使用演示用的Mock STT服务
    final sttService = useMemoized(() => null); // 暂时不使用STT服务
    final ttsService = useMemoized(() => TTSService());
    final ambientService = ref.watch(ambientSoundServiceProvider);
    
    // 状态管理
    final isInitialized = useState(false);
    final companionState = useState<CompanionState>(CompanionState.inactive);
    final stateEvents = useState<List<CompanionStateEvent>>([]);
    final sessionDuration = useState(0);
    final showControls = useState(true);
    final statusInfo = useState<Map<String, dynamic>>({});
    
    // 动画控制
    final fadeController = useAnimationController(
      duration: const Duration(milliseconds: 500),
    );
    
    final pulseController = useAnimationController(
      duration: const Duration(milliseconds: 1500),
    );
    
    final rotationController = useAnimationController(
      duration: const Duration(seconds: 8),
    );

    // 初始化
    useEffect(() {
      _initializeAiCompanion(
        orchestrator,
        audioService,
        sttService,
        ttsService,
        isInitialized,
      );
      
      return () {
        orchestrator.stop();
      };
    }, []);

    // 监听状态变化
    useEffect(() {
      final stateSubscription = orchestrator.stateStream.listen((state) {
        companionState.value = state;
        _updateAnimationsForState(state, pulseController, rotationController);
      });

      final eventSubscription = orchestrator.stateEventStream.listen((event) {
        stateEvents.value = [...stateEvents.value, event];
        if (stateEvents.value.length > 10) {
          stateEvents.value = stateEvents.value.sublist(stateEvents.value.length - 10);
        }
      });

      // 定期更新状态信息
      final statusTimer = Timer.periodic(Duration(seconds: 2), (timer) {
        if (isInitialized.value) {
          statusInfo.value = orchestrator.getStatusInfo();
        }
      });

      return () {
        stateSubscription.cancel();
        eventSubscription.cancel();
        statusTimer.cancel();
      };
    }, []);

    // 会话计时器
    useEffect(() {
      Timer? sessionTimer;
      
      if (companionState.value != CompanionState.inactive) {
        sessionTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
          sessionDuration.value++;
        });
      }
      
      return () {
        sessionTimer?.cancel();
      };
    }, [companionState.value]);

    // 控制面板自动隐藏
    useEffect(() {
      Timer? hideTimer;
      
      if (showControls.value && companionState.value == CompanionState.listening) {
        hideTimer = Timer(const Duration(seconds: 5), () {
          showControls.value = false;
          fadeController.reverse();
        });
      }
      
      return () {
        hideTimer?.cancel();
      };
    }, [showControls.value, companionState.value]);

    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: () {
          showControls.value = !showControls.value;
          if (showControls.value) {
            fadeController.forward();
          } else {
            fadeController.reverse();
          }
        },
        child: Stack(
          children: [
            // 背景效果
            Container(
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  center: Alignment.center,
                  radius: 1.0,
                  colors: [
                    Colors.deepPurple.withOpacity(0.1),
                    Colors.black,
                  ],
                ),
              ),
            ),
            
            // 中央AI状态可视化
            Center(
              child: _buildCentralVisualization(
                companionState.value,
                pulseController,
                rotationController,
                statusInfo.value,
              ),
            ),
            
            // 顶部状态栏
            Positioned(
              top: MediaQuery.of(context).padding.top + 20,
              left: 20,
              right: 20,
              child: _buildStatusBar(
                companionState.value,
                sessionDuration.value,
                statusInfo.value,
              ),
            ),
            
            // 底部控制面板
            Positioned(
              bottom: MediaQuery.of(context).padding.bottom + 20,
              left: 20,
              right: 20,
              child: FadeTransition(
                opacity: fadeController,
                child: _buildControlPanel(
                  context,
                  orchestrator,
                  companionState.value,
                  isInitialized.value,
                  ambientService,
                ),
              ),
            ),
            
            // 最近事件显示
            if (stateEvents.value.isNotEmpty)
              Positioned(
                top: MediaQuery.of(context).padding.top + 80,
                left: 20,
                right: 20,
                child: _buildRecentEvents(stateEvents.value),
              ),
          ],
        ),
      ),
    );
  }

  /// 初始化AI陪伴服务
  Future<void> _initializeAiCompanion(
    AiCompanionOrchestrator orchestrator,
    ParallelAudioIOService audioService,
    StreamingSttService? sttService,
    TTSService ttsService,
    ValueNotifier<bool> isInitialized,
  ) async {
    try {
      // 初始化编排器
      final success = await orchestrator.initialize(
        audioService: audioService,
        sttService: sttService,
        ttsService: ttsService,
      );
      
      if (success) {
        isInitialized.value = true;
        
        // 自动启动AI陪伴模式
        await orchestrator.start();
      }
    } catch (e) {
      debugPrint('Failed to initialize AI companion: $e');
    }
  }

  /// 根据状态更新动画
  void _updateAnimationsForState(
    CompanionState state,
    AnimationController pulseController,
    AnimationController rotationController,
  ) {
    switch (state) {
      case CompanionState.listening:
        pulseController.repeat(reverse: true);
        rotationController.repeat();
        break;
      case CompanionState.empathizing:
        pulseController.forward();
        rotationController.stop();
        break;
      case CompanionState.thinking:
        pulseController.stop();
        rotationController.repeat();
        break;
      case CompanionState.responding:
        pulseController.repeat();
        rotationController.forward();
        break;
      default:
        pulseController.stop();
        rotationController.stop();
    }
  }

  /// 构建中央可视化
  Widget _buildCentralVisualization(
    CompanionState state,
    AnimationController pulseController,
    AnimationController rotationController,
    Map<String, dynamic> statusInfo,
  ) {
    return AnimatedBuilder(
      animation: Listenable.merge([pulseController, rotationController]),
      builder: (context, child) {
        return Container(
          width: 200,
          height: 200,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: _getColorsForState(state),
              stops: const [0.0, 0.7, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: _getColorsForState(state)[1].withOpacity(0.3),
                blurRadius: 30 * (1 + pulseController.value * 0.5),
                spreadRadius: 10 * (1 + pulseController.value * 0.3),
              ),
            ],
          ),
          child: Transform.rotate(
            angle: rotationController.value * 2 * pi,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _getIconForState(state),
                    size: 60,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _getTextForState(state),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 获取状态对应的颜色
  List<Color> _getColorsForState(CompanionState state) {
    switch (state) {
      case CompanionState.listening:
        return [Colors.blue.shade300, Colors.blue.shade600, Colors.blue.shade900];
      case CompanionState.empathizing:
        return [Colors.pink.shade300, Colors.pink.shade600, Colors.pink.shade900];
      case CompanionState.thinking:
        return [Colors.purple.shade300, Colors.purple.shade600, Colors.purple.shade900];
      case CompanionState.responding:
        return [Colors.green.shade300, Colors.green.shade600, Colors.green.shade900];
      default:
        return [Colors.grey.shade300, Colors.grey.shade600, Colors.grey.shade900];
    }
  }

  /// 获取状态对应的图标
  IconData _getIconForState(CompanionState state) {
    switch (state) {
      case CompanionState.listening:
        return Icons.hearing;
      case CompanionState.empathizing:
        return Icons.favorite;
      case CompanionState.thinking:
        return Icons.psychology;
      case CompanionState.responding:
        return Icons.chat;
      default:
        return Icons.power_off;
    }
  }

  /// 获取状态对应的文本
  String _getTextForState(CompanionState state) {
    switch (state) {
      case CompanionState.listening:
        return '倾听中';
      case CompanionState.empathizing:
        return '共鸣中';
      case CompanionState.thinking:
        return '思考中';
      case CompanionState.responding:
        return '回应中';
      case CompanionState.initializing:
        return '初始化中';
      default:
        return '未激活';
    }
  }

  /// 构建状态栏
  Widget _buildStatusBar(
    CompanionState state,
    int sessionDuration,
    Map<String, dynamic> statusInfo,
  ) {
    final minutes = sessionDuration ~/ 60;
    final seconds = sessionDuration % 60;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getColorsForState(state)[1],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              _getTextForState(state),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (statusInfo.isNotEmpty)
            Text(
              '对话: ${statusInfo['conversationState']?['historyCount'] ?? 0}',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 12,
              ),
            ),
        ],
      ),
    );
  }

  /// 构建控制面板
  Widget _buildControlPanel(
    BuildContext context,
    AiCompanionOrchestrator orchestrator,
    CompanionState state,
    bool isInitialized,
    AmbientSoundService ambientService,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 返回按钮
          _buildControlButton(
            icon: Icons.arrow_back,
            label: '返回',
            onPressed: () async {
              await orchestrator.stop();
              ambientService.stop();
              Navigator.of(context).pop();
            },
          ),

          // 暂停/继续按钮
          if (state == CompanionState.listening)
            _buildControlButton(
              icon: Icons.pause,
              label: '暂停',
              onPressed: () async {
                await orchestrator.stop();
              },
            )
          else if (state == CompanionState.inactive && isInitialized)
            _buildControlButton(
              icon: Icons.play_arrow,
              label: '开始',
              onPressed: () async {
                await orchestrator.start();
              },
            ),

          // 设置按钮
          _buildControlButton(
            icon: Icons.settings,
            label: '设置',
            onPressed: () {
              _showSettingsDialog(context, orchestrator);
            },
          ),
        ],
      ),
    );
  }

  /// 构建控制按钮
  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          onPressed: onPressed,
          icon: Icon(icon, color: Colors.white),
          iconSize: 32,
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  /// 构建最近事件显示
  Widget _buildRecentEvents(List<CompanionStateEvent> events) {
    final recentEvents = events.length > 3 ? events.sublist(events.length - 3) : events;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.2),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: recentEvents.reversed.map((event) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Text(
              '${event.timestamp.toString().substring(11, 19)} - ${event.message ?? event.state.name}',
              style: const TextStyle(
                color: Colors.white60,
                fontSize: 10,
                fontFamily: 'monospace',
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 显示设置对话框
  void _showSettingsDialog(BuildContext context, AiCompanionOrchestrator orchestrator) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('AI陪伴设置'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('对话超时时间'),
              subtitle: const Text('5秒'),
              trailing: const Icon(Icons.timer),
            ),
            ListTile(
              title: const Text('共鸣回应时长'),
              subtitle: const Text('800毫秒'),
              trailing: const Icon(Icons.favorite),
            ),
            ListTile(
              title: const Text('长停顿阈值'),
              subtitle: const Text('8秒'),
              trailing: const Icon(Icons.pause),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}
