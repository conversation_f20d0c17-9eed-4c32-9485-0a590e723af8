/// AI陪伴编排器演示界面
/// 
/// 展示完整的AI陪伴功能，包括状态管理、服务集成等

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../services/ai_companion_orchestrator.dart';
import '../services/stt/streaming_stt_service.dart';
import '../services/stt/stt_models.dart';
import 'dart:async';
import 'dart:typed_data';

/// Mock TTS Service for Demo
class MockTtsService {
  Future<void> speak(String text) async {
    print('🔊 Mock TTS: "$text"');
    // 模拟TTS播放时间
    await Future.delayed(Duration(milliseconds: text.length * 50));
  }

  Future<void> stop() async {
    print('🔇 Mock TTS: Stopped');
  }
}

/// Mock STT Service for Demo
class DemoSttService extends StreamingSttService {
  final StreamController<SttResult> _resultController = StreamController<SttResult>.broadcast();
  final StreamController<SttError> _errorController = StreamController<SttError>.broadcast();
  final StreamController<SttConnectionState> _stateController = StreamController<SttConnectionState>.broadcast();
  final StreamController<void> _endOfSpeechController = StreamController<void>.broadcast();
  
  SttConnectionState _state = SttConnectionState.disconnected;
  bool _isListening = false;

  @override
  SttConnectionState get connectionState => _state;

  @override
  SttConfig get config => SttConfig(
    language: 'zh-cn',
    sampleRate: 16000,
    enableInterimResults: true,
  );

  @override
  SttStats get stats => SttStats(
    totalDuration: 1000,
    totalCharacters: 100,
    averageConfidence: 0.9,
    networkLatency: 100,
    errorCount: 0,
  );

  @override
  bool get isListening => _isListening;

  @override
  Stream<SttResult> get onResult => _resultController.stream;

  @override
  Stream<SttError> get onError => _errorController.stream;

  @override
  Stream<SttConnectionState> get onConnectionStateChanged => _stateController.stream;

  @override
  Stream<void> get onEndOfSpeech => _endOfSpeechController.stream;

  @override
  Future<bool> initialize(SttConfig config) async {
    _state = SttConnectionState.connected;
    _stateController.add(_state);
    return true;
  }

  @override
  Future<void> startListening() async {
    _isListening = true;
    _state = SttConnectionState.listening;
    _stateController.add(_state);
  }

  @override
  Future<void> stopListening() async {
    _isListening = false;
    _state = SttConnectionState.connected;
    _stateController.add(_state);
  }

  @override
  void sendAudioData(Uint8List audioData) {}

  @override
  Future<void> reset() async {
    _isListening = false;
    _state = SttConnectionState.connected;
    _stateController.add(_state);
  }

  @override
  Future<void> disconnect() async {
    _isListening = false;
    _state = SttConnectionState.disconnected;
    _stateController.add(_state);
  }

  @override
  Future<List<String>> getSupportedLanguages() async {
    return ['zh-CN', 'en-US'];
  }

  @override
  Future<int> testConnection() async {
    return 100;
  }

  // Demo helper methods
  void simulateResult(String text, {bool isFinal = false}) {
    final result = SttResult(
      text: text,
      isFinal: isFinal,
      confidence: 0.9,
      timestamp: DateTime.now(),
    );
    _resultController.add(result);
  }

  @override
  void dispose() {
    _resultController.close();
    _errorController.close();
    _stateController.close();
    _endOfSpeechController.close();
    super.dispose();
  }
}

class AiCompanionDemoView extends HookWidget {
  const AiCompanionDemoView({super.key});

  @override
  Widget build(BuildContext context) {
    final orchestrator = useMemoized(() => AiCompanionOrchestrator.instance);
    final demoSttService = useMemoized(() => DemoSttService());
    final mockTtsService = useMemoized(() => MockTtsService());
    
    final currentState = useState<CompanionState>(CompanionState.inactive);
    final stateEvents = useState<List<CompanionStateEvent>>([]);
    final conversationBuffer = useState<String>('');
    final isInitialized = useState<bool>(false);

    // 监听状态变化
    useEffect(() {
      final stateSubscription = orchestrator.stateStream.listen((state) {
        currentState.value = state;
      });

      final eventSubscription = orchestrator.stateEventStream.listen((event) {
        stateEvents.value = [...stateEvents.value, event];
        if (stateEvents.value.length > 20) {
          stateEvents.value = stateEvents.value.sublist(stateEvents.value.length - 20);
        }
      });

      return () {
        stateSubscription.cancel();
        eventSubscription.cancel();
      };
    }, []);

    // 初始化编排器
    final initializeOrchestrator = useCallback(() async {
      try {
        final success = await orchestrator.initialize(
          sttService: demoSttService,
          // 暂时不传入TTS服务，避免设备兼容性问题
          // ttsService: mockTtsService,
        );
        isInitialized.value = success;
        
        if (success) {
          stateEvents.value = [...stateEvents.value, 
            CompanionStateEvent(
              state: CompanionState.inactive,
              timestamp: DateTime.now(),
              message: '✅ AI陪伴编排器初始化成功',
            )
          ];
        }
      } catch (e) {
        stateEvents.value = [...stateEvents.value,
          CompanionStateEvent(
            state: CompanionState.error,
            timestamp: DateTime.now(),
            message: '❌ 初始化失败: $e',
          )
        ];
      }
    }, []);

    // 启动AI陪伴
    final startCompanion = useCallback(() async {
      final success = await orchestrator.start();
      if (success) {
        stateEvents.value = [...stateEvents.value,
          CompanionStateEvent(
            state: currentState.value,
            timestamp: DateTime.now(),
            message: '🚀 AI陪伴模式启动成功',
          )
        ];
      }
    }, []);

    // 停止AI陪伴
    final stopCompanion = useCallback(() async {
      await orchestrator.stop();
      stateEvents.value = [...stateEvents.value,
        CompanionStateEvent(
          state: CompanionState.inactive,
          timestamp: DateTime.now(),
          message: '⏹️ AI陪伴模式已停止',
        )
      ];
    }, []);

    // 模拟语音输入
    final simulateInput = useCallback((String text) {
      demoSttService.simulateResult(text, isFinal: true);
      stateEvents.value = [...stateEvents.value,
        CompanionStateEvent(
          state: currentState.value,
          timestamp: DateTime.now(),
          message: '🎤 模拟输入: "$text"',
        )
      ];
    }, []);

    return Scaffold(
      appBar: AppBar(
        title: const Text('AI陪伴编排器演示'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 状态显示区域
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'AI陪伴状态',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          _getStateIcon(currentState.value),
                          color: _getStateColor(currentState.value),
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _getStateText(currentState.value),
                          style: TextStyle(
                            color: _getStateColor(currentState.value),
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Text('初始化状态: ${isInitialized.value ? "已初始化" : "未初始化"}'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 控制按钮区域
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '控制面板',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        if (!isInitialized.value)
                          ElevatedButton.icon(
                            onPressed: initializeOrchestrator,
                            icon: const Icon(Icons.settings),
                            label: const Text('初始化编排器'),
                          ),
                        
                        if (isInitialized.value && currentState.value == CompanionState.inactive)
                          ElevatedButton.icon(
                            onPressed: startCompanion,
                            icon: const Icon(Icons.play_arrow),
                            label: const Text('启动AI陪伴'),
                            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                          ),
                        
                        if (currentState.value != CompanionState.inactive)
                          ElevatedButton.icon(
                            onPressed: stopCompanion,
                            icon: const Icon(Icons.stop),
                            label: const Text('停止AI陪伴'),
                            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                          ),
                        
                        if (currentState.value == CompanionState.listening)
                          ElevatedButton.icon(
                            onPressed: () => simulateInput('我今天很累'),
                            icon: const Icon(Icons.mic),
                            label: const Text('模拟: 我很累'),
                          ),
                        
                        if (currentState.value == CompanionState.listening)
                          ElevatedButton.icon(
                            onPressed: () => simulateInput('你好吗？'),
                            icon: const Icon(Icons.help),
                            label: const Text('模拟: 问问题'),
                          ),
                        
                        ElevatedButton.icon(
                          onPressed: () {
                            stateEvents.value = [];
                          },
                          icon: const Icon(Icons.clear),
                          label: const Text('清除日志'),
                          style: ElevatedButton.styleFrom(backgroundColor: Colors.grey),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 事件日志区域
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '事件日志 (${stateEvents.value.length})',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: ListView.builder(
                          itemCount: stateEvents.value.length,
                          itemBuilder: (context, index) {
                            final event = stateEvents.value[stateEvents.value.length - 1 - index];
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 2.0),
                              child: Text(
                                '${event.timestamp.toString().substring(11, 19)} - ${event.message ?? event.state.name}',
                                style: const TextStyle(fontSize: 12, fontFamily: 'monospace'),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getStateIcon(CompanionState state) {
    switch (state) {
      case CompanionState.inactive:
        return Icons.power_off;
      case CompanionState.initializing:
        return Icons.settings;
      case CompanionState.listening:
        return Icons.hearing;
      case CompanionState.empathizing:
        return Icons.favorite;
      case CompanionState.thinking:
        return Icons.psychology;
      case CompanionState.responding:
        return Icons.chat;
      case CompanionState.error:
        return Icons.error;
    }
  }

  Color _getStateColor(CompanionState state) {
    switch (state) {
      case CompanionState.inactive:
        return Colors.grey;
      case CompanionState.initializing:
        return Colors.orange;
      case CompanionState.listening:
        return Colors.blue;
      case CompanionState.empathizing:
        return Colors.pink;
      case CompanionState.thinking:
        return Colors.purple;
      case CompanionState.responding:
        return Colors.green;
      case CompanionState.error:
        return Colors.red;
    }
  }

  String _getStateText(CompanionState state) {
    switch (state) {
      case CompanionState.inactive:
        return '未激活';
      case CompanionState.initializing:
        return '初始化中';
      case CompanionState.listening:
        return '监听中';
      case CompanionState.empathizing:
        return '共鸣中';
      case CompanionState.thinking:
        return '思考中';
      case CompanionState.responding:
        return '回应中';
      case CompanionState.error:
        return '错误';
    }
  }
}
