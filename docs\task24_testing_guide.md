# Task 24 共鸣引擎测试指南

## 🎯 测试概述

Task 24 实现了一个完整的实时共鸣触发引擎，包含以下核心组件：

1. **规则引擎** - 管理触发规则和条件
2. **条件检测器** - 分析文本和语音模式
3. **智能回应选择器** - 选择合适的回应
4. **TTS时机控制器** - 控制播放时机
5. **AI情绪分析器** - DeepSeek AI增强分析

## 🧪 测试方法

### 方法1: 运行自动化测试脚本

```bash
# 运行完整的测试套件
dart test_task24.dart
```

这将自动运行所有相关的单元测试和集成测试。

### 方法2: 手动运行单个测试文件

```bash
# 测试数据模型
flutter test test/services/resonance/resonance_models_test.dart

# 测试条件检测器
flutter test test/services/resonance/condition_evaluator_test.dart

# 测试回应选择器
flutter test test/services/resonance/response_selector_test.dart

# 测试TTS时机控制器
flutter test test/services/resonance/tts_timing_controller_test.dart

# 测试主引擎服务
flutter test test/services/resonance/resonance_engine_service_test.dart

# 测试AI情绪分析器
flutter test test/services/resonance/ai_emotion_analyzer_test.dart
```

### 方法3: 运行交互式演示

1. **添加演示页面到应用**

在 `lib/main.dart` 中添加路由：

```dart
import 'demo/resonance_engine_demo.dart';

// 在路由中添加
'/resonance-demo': (context) => const ResonanceEngineDemoView(),
```

2. **运行应用**

```bash
flutter run
```

3. **导航到演示页面**

在应用中导航到 `/resonance-demo` 路由。

## 🔍 测试用例说明

### 1. 数据模型测试 (resonance_models_test.dart)

- ✅ JSON序列化/反序列化
- ✅ 规则验证逻辑
- ✅ 条件类型匹配
- ✅ 动作配置验证

### 2. 条件检测器测试 (condition_evaluator_test.dart)

- ✅ 关键词匹配（精确匹配、大小写敏感）
- ✅ 停顿检测（时间阈值、上下文分析）
- ✅ 情绪分析（AI增强 + 传统回退）
- ✅ 语速分析（实时计算）
- ✅ 规则优先级排序

### 3. 回应选择器测试 (response_selector_test.dart)

- ✅ 随机选择策略
- ✅ 上下文感知选择
- ✅ 情绪基础选择
- ✅ 防重复选择
- ✅ 历史记录管理

### 4. TTS时机控制器测试 (tts_timing_controller_test.dart)

- ✅ 播放状态管理
- ✅ 智能时机检测
- ✅ 错误处理和恢复
- ✅ 统计信息追踪
- ✅ 强制停止功能

### 5. 主引擎服务测试 (resonance_engine_service_test.dart)

- ✅ 服务生命周期管理
- ✅ STT集成和事件处理
- ✅ 规则评估流程
- ✅ 冷却机制（全局和规则特定）
- ✅ TTS集成和播放控制

### 6. AI情绪分析器测试 (ai_emotion_analyzer_test.dart)

- ✅ DeepSeek API集成
- ✅ 情绪分析准确性
- ✅ 语义匹配功能
- ✅ 缓存机制
- ✅ 错误处理和回退

## 🎮 交互式演示功能

演示界面提供以下测试功能：

### 自动化测试用例

1. **疲惫关键词测试**
   - 输入: "我今天好累啊"
   - 期望: 触发疲惫检测规则

2. **负面情绪测试**
   - 输入: "我很难过，感觉很痛苦"
   - 期望: 触发情绪分析规则

3. **停顿检测测试**
   - 输入: "我想说..." + 2秒停顿
   - 期望: 触发停顿检测规则

4. **复合条件测试**
   - 输入: "我真的很累，不想说话了"
   - 期望: 可能触发多个规则

### 实时监控

- 📊 引擎状态显示
- 📝 测试结果日志
- 🎯 触发的回应事件
- ⏱️ 时间戳和元数据

## 🔧 故障排除

### 常见问题

1. **AI分析失败**
   - 原因: DeepSeek API密钥未配置
   - 解决: 系统会自动回退到传统分析

2. **TTS播放失败**
   - 原因: TTS服务未初始化
   - 解决: 检查TTS服务配置

3. **测试超时**
   - 原因: 网络延迟或API响应慢
   - 解决: 增加测试超时时间

### 调试技巧

1. **启用详细日志**
   ```dart
   debugPrint('详细的调试信息');
   ```

2. **检查服务状态**
   ```dart
   final status = resonanceEngine.getCooldownStatus();
   final ttsStatus = resonanceEngine.getTtsStatus();
   ```

3. **监控事件流**
   ```dart
   resonanceEngine.onResponse.listen((event) {
     print('回应事件: ${event.responseText}');
   });
   ```

## 📈 性能基准

### 预期性能指标

- **响应延迟**: < 200ms (不含AI分析)
- **AI分析延迟**: < 2s (含网络请求)
- **内存使用**: < 50MB (缓存和历史记录)
- **准确率**: > 85% (关键词匹配)

### 测试通过标准

- ✅ 所有单元测试通过
- ✅ 集成测试无错误
- ✅ 演示界面功能正常
- ✅ 响应时间在预期范围内
- ✅ 内存使用稳定

## 🎉 测试完成确认

当所有测试通过后，您应该看到：

1. **自动化测试**: 所有测试文件都显示 ✅ 通过
2. **演示界面**: 能够成功初始化引擎并响应测试用例
3. **功能验证**: 各种条件都能正确触发相应的回应
4. **性能表现**: 响应及时，无明显延迟或错误

恭喜！Task 24 共鸣引擎已经准备就绪，可以进入下一阶段的UI开发了！🚀
