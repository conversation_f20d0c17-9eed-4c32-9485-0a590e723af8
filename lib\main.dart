import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:echo_cave/views/auth_gate_view.dart';
import 'package:quick_actions/quick_actions.dart';
import 'package:echo_cave/services/audio_recording_service.dart';
import 'package:echo_cave/views/main_view.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:echo_cave/services/config_service.dart';
import 'package:echo_cave/demo/resonance_engine_demo.dart';
import 'package:echo_cave/demo/ai_companion_demo.dart';
import 'package:echo_cave/views/ai_companion_immersive_view.dart';

// Global navigator key is useful for navigation from outside the widget tree,
// for example, from the quick actions callback.
final navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  // Ensure Flutter bindings are initialized before doing async work
  WidgetsFlutterBinding.ensureInitialized();

  // 加载环境变量
  await dotenv.load(fileName: ".env");

  // 打印配置信息
  ConfigService.instance.printConfig();

  // Securely store the API key if it's not already there
  await _setupApiKeys();

  final container = ProviderContainer();
  const QuickActions quickActions = QuickActions();

  quickActions.initialize((String shortcutType) {
    if (shortcutType == 'action_record') {
      // Access the provider from the container and start recording
      final audioService = container.read(audioRecordingServiceProvider);
      // Ensure it's not already recording from a previous (e.g., failed) action
      if (!audioService.isRecording) {
        audioService.startRecording();
        print('Shortcut recording started!');
      }
    }
  });

  quickActions.setShortcutItems(<ShortcutItem>[
    const ShortcutItem(
      type: 'action_record',
      localizedTitle: 'New Echo',
      icon:
          'ic_launcher', // Assumes you have an icon named 'ic_launcher' in native resources
    ),
  ]);

  runApp(
    ProviderScope(
      parent: container,
      child: const EchoCaveApp(),
    ),
  );
}

Future<void> _setupApiKeys() async {
  const storage = FlutterSecureStorage();
  const keyName = 'deepseek_api_key';

  // 从配置服务读取API key
  final apiKey = ConfigService.instance.deepseekApiKey;

  if (apiKey.isNotEmpty) {
    final existingKey = await storage.read(key: keyName);
    if (existingKey != apiKey) {
      await storage.write(key: keyName, value: apiKey);
      debugPrint('DeepSeek API Key has been updated from .env file.');
    }
  } else {
    debugPrint('Warning: DEEPSEEK_API_KEY not found in .env file.');
  }
}

class EchoCaveApp extends StatelessWidget {
  const EchoCaveApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      navigatorKey: navigatorKey,
      title: 'Echo Cave',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const AuthGateView(),
      routes: {
        '/resonance-demo': (context) => const ResonanceEngineDemoView(),
        '/ai-companion-demo': (context) => const AiCompanionDemoView(),
        '/ai-companion-immersive': (context) => const AiCompanionImmersiveView(),
      },
    );
  }
}
