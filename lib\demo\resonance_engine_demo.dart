/// Task 24 共鸣引擎演示和测试界面
/// 
/// 这个文件提供了一个完整的演示界面，用于测试共鸣引擎的所有功能

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../services/resonance/resonance_engine_service.dart';
import '../services/resonance/resonance_models.dart';
import '../services/resonance/resonance_rule_loader.dart';
import '../services/tts_service.dart';
import '../services/stt/streaming_stt_service.dart';
import '../services/stt/stt_models.dart';
import 'dart:async';
import 'dart:typed_data';

/// Mock STT Service for Demo
class DemoSttService extends StreamingSttService {
  final StreamController<SttResult> _resultController = StreamController<SttResult>.broadcast();
  final StreamController<SttError> _errorController = StreamController<SttError>.broadcast();
  final StreamController<SttConnectionState> _stateController = StreamController<SttConnectionState>.broadcast();
  final StreamController<void> _endOfSpeechController = StreamController<void>.broadcast();
  
  SttConnectionState _state = SttConnectionState.disconnected;
  bool _isListening = false;

  @override
  SttConnectionState get connectionState => _state;

  @override
  SttConfig get config => SttConfig(
    language: 'zh-cn',
    sampleRate: 16000,
    enableInterimResults: true,
  );

  @override
  SttStats get stats => SttStats(
    totalDuration: 1000,
    totalCharacters: 100,
    averageConfidence: 0.9,
    networkLatency: 100,
    errorCount: 0,
  );

  @override
  bool get isListening => _isListening;

  @override
  Stream<SttResult> get onResult => _resultController.stream;

  @override
  Stream<SttError> get onError => _errorController.stream;

  @override
  Stream<SttConnectionState> get onConnectionStateChanged => _stateController.stream;

  @override
  Stream<void> get onEndOfSpeech => _endOfSpeechController.stream;

  @override
  Future<bool> initialize(SttConfig config) async {
    _state = SttConnectionState.connected;
    _stateController.add(_state);
    return true;
  }

  @override
  Future<void> startListening() async {
    _isListening = true;
    _state = SttConnectionState.listening;
    _stateController.add(_state);
  }

  @override
  Future<void> stopListening() async {
    _isListening = false;
    _state = SttConnectionState.connected;
    _stateController.add(_state);
  }

  @override
  void sendAudioData(Uint8List audioData) {
    // Mock implementation
  }

  @override
  Future<void> reset() async {
    _isListening = false;
    _state = SttConnectionState.connected;
    _stateController.add(_state);
  }

  @override
  Future<void> disconnect() async {
    _isListening = false;
    _state = SttConnectionState.disconnected;
    _stateController.add(_state);
  }

  @override
  Future<List<String>> getSupportedLanguages() async {
    return ['zh-CN', 'en-US'];
  }

  @override
  Future<int> testConnection() async {
    return 100; // 100ms latency
  }

  // Demo helper methods
  void simulateResult(String text, {bool isFinal = false}) {
    final result = SttResult(
      text: text,
      isFinal: isFinal,
      confidence: 0.9,
      timestamp: DateTime.now(),
    );
    _resultController.add(result);
  }

  void simulateEndOfSpeech() {
    _endOfSpeechController.add(null);
  }

  @override
  void dispose() {
    _resultController.close();
    _errorController.close();
    _stateController.close();
    _endOfSpeechController.close();
    super.dispose();
  }
}

class ResonanceEngineDemoView extends HookWidget {
  const ResonanceEngineDemoView({super.key});

  @override
  Widget build(BuildContext context) {
    final resonanceEngine = useMemoized(() => ResonanceEngineService());
    final demoSttService = useMemoized(() => DemoSttService());
    final ttsService = useMemoized(() => TTSService());
    
    final isEngineRunning = useState(false);
    final responseEvents = useState<List<ResonanceResponseEvent>>([]);
    final engineStatus = useState<String>('未初始化');
    final testResults = useState<List<String>>([]);

    // 初始化引擎
    final initializeEngine = useCallback(() async {
      try {
        engineStatus.value = '正在初始化...';

        // 创建测试规则集
        final testRuleSet = ResonanceRuleSet(
          version: '1.0.0',
          name: 'Demo Rules',
          description: 'Task 24 演示规则集',
          rules: [
            ResonanceRule(
              id: 'tired_rule',
              name: '疲惫检测',
              description: '检测用户表达疲惫',
              condition: KeywordCondition(keywords: ['累', '疲惫', '好累']),
              action: ResonanceAction(
                type: ResonanceActionType.empathyResponse,
                responsePool: ['我理解你的疲惫', '休息一下吧', '辛苦了'],
              ),
              priority: 2,
            ),
            ResonanceRule(
              id: 'sad_rule',
              name: '难过检测',
              description: '检测用户难过情绪',
              condition: EmotionCondition(
                tendency: EmotionTendency.negative,
                threshold: 0.6,
              ),
              action: ResonanceAction(
                type: ResonanceActionType.comfort,
                responsePool: ['我在这里陪着你', '别难过', '会好起来的'],
              ),
              priority: 3,
            ),
            ResonanceRule(
              id: 'pause_rule',
              name: '停顿检测',
              description: '检测用户说话停顿',
              condition: PauseCondition(
                minDuration: Duration(milliseconds: 1500),
                maxDuration: Duration(milliseconds: 5000),
              ),
              action: ResonanceAction(
                type: ResonanceActionType.acknowledgment,
                responsePool: ['嗯', '我在听', '继续说吧'],
              ),
              priority: 1,
            ),
          ],
        );

        // 首先初始化引擎（跳过默认规则加载）
        // 注意：我们不调用 initialize() 因为它会尝试加载默认规则文件
        // 直接设置引擎状态为已初始化
        resonanceEngine.setStateForTesting(ResonanceEngineState.initialized);
        testResults.value = [...testResults.value, '✅ 引擎核心初始化完成（跳过默认规则）'];

        // 加载规则
        await resonanceEngine.loadRules(testRuleSet);
        testResults.value = [...testResults.value, '✅ 规则加载完成'];

        // 启动引擎
        await resonanceEngine.start(demoSttService, ttsService: ttsService);
        testResults.value = [...testResults.value, '✅ 引擎启动完成'];

        // 监听回应事件
        resonanceEngine.onResponse.listen((event) {
          responseEvents.value = [...responseEvents.value, event];
        });

        isEngineRunning.value = true;
        engineStatus.value = '引擎运行中';
        testResults.value = [...testResults.value, '✅ 引擎初始化成功'];

      } catch (e) {
        engineStatus.value = '初始化失败: $e';
        testResults.value = [...testResults.value, '❌ 引擎初始化失败: $e'];
      }
    }, []);

    // 测试用例
    final runTestCase = useCallback((String testName, String testText, {Duration? delay}) async {
      if (!isEngineRunning.value) {
        testResults.value = [...testResults.value, '❌ $testName: 引擎未运行'];
        return;
      }

      testResults.value = [...testResults.value, '🧪 开始测试: $testName'];
      
      // 模拟STT结果
      demoSttService.simulateResult(testText, isFinal: true);
      
      if (delay != null) {
        await Future.delayed(delay);
        demoSttService.simulateEndOfSpeech();
      }
      
      // 等待处理
      await Future.delayed(const Duration(milliseconds: 500));
      
      testResults.value = [...testResults.value, '📝 输入文本: "$testText"'];
    }, [isEngineRunning.value]);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Task 24 共鸣引擎演示'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 引擎状态
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '引擎状态',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      engineStatus.value,
                      style: TextStyle(
                        color: isEngineRunning.value ? Colors.green : Colors.orange,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    if (!isEngineRunning.value)
                      ElevatedButton(
                        onPressed: initializeEngine,
                        child: const Text('初始化引擎'),
                      ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 测试按钮
            if (isEngineRunning.value) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '测试用例',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: [
                          ElevatedButton(
                            onPressed: () => runTestCase('关键词测试', '我今天好累啊'),
                            child: const Text('测试疲惫关键词'),
                          ),
                          ElevatedButton(
                            onPressed: () => runTestCase('情绪测试', '我很难过，感觉很痛苦'),
                            child: const Text('测试负面情绪'),
                          ),
                          ElevatedButton(
                            onPressed: () => runTestCase('停顿测试', '我想说...', delay: Duration(milliseconds: 2000)),
                            child: const Text('测试停顿检测'),
                          ),
                          ElevatedButton(
                            onPressed: () => runTestCase('复合测试', '我真的很累，不想说话了'),
                            child: const Text('测试复合条件'),
                          ),
                          ElevatedButton(
                            onPressed: () {
                              testResults.value = [];
                              responseEvents.value = [];
                            },
                            style: ElevatedButton.styleFrom(backgroundColor: Colors.grey),
                            child: const Text('清除结果'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
            ],
            
            // 结果显示
            Expanded(
              child: Row(
                children: [
                  // 测试结果
                  Expanded(
                    child: Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '测试结果',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const SizedBox(height: 8),
                            Expanded(
                              child: ListView.builder(
                                itemCount: testResults.value.length,
                                itemBuilder: (context, index) {
                                  return Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 2.0),
                                    child: Text(
                                      testResults.value[index],
                                      style: const TextStyle(fontSize: 12),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // 回应事件
                  Expanded(
                    child: Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '触发的回应 (${responseEvents.value.length})',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const SizedBox(height: 8),
                            Expanded(
                              child: ListView.builder(
                                itemCount: responseEvents.value.length,
                                itemBuilder: (context, index) {
                                  final event = responseEvents.value[index];
                                  return Card(
                                    margin: const EdgeInsets.symmetric(vertical: 4.0),
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            '规则: ${event.ruleName}',
                                            style: const TextStyle(fontWeight: FontWeight.bold),
                                          ),
                                          Text('回应: "${event.responseText}"'),
                                          Text(
                                            '时间: ${event.timestamp.toString().substring(11, 19)}',
                                            style: const TextStyle(fontSize: 10, color: Colors.grey),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
