import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:just_audio/just_audio.dart';
import '../services/parallel_audio_io_service.dart';
import 'main_view.dart';

/// AEC测试界面
/// 用于验证并行音频I/O与回声消除技术
class AECTestView extends HookConsumerWidget {
  const AECTestView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final aecService = useState<ParallelAudioIOService?>(null);
    final isInitialized = useState(false);
    final isTestRunning = useState(false);
    final currentTestMode = useState<AECTestMode?>(null);
    final testResults = useState<List<AECTestResult>>([]);
    final audioPlayer = useState<AudioPlayer?>(null);
    final playingIndex = useState<int?>(null);

    // 获取背景音服务
    final ambientService = ref.read(ambientSoundServiceProvider);

    // 进入页面时暂停背景音，离开时恢复
    useEffect(() {
      ambientService.pause();
      return () {
        ambientService.resume();
      };
    }, []);

    // 初始化音频播放器
    useEffect(() {
      final player = AudioPlayer();
      audioPlayer.value = player;

      return () {
        player.dispose();
      };
    }, []);

    // 初始化服务
    useEffect(() {
      Future.microtask(() async {
        final service = ParallelAudioIOService();
        final success = await service.initialize();
        if (success) {
          aecService.value = service;
          isInitialized.value = true;

          // 监听测试结果变化
          service.addListener(() {
            testResults.value = service.testResults;
          });
        }
      });

      return () {
        aecService.value?.dispose();
      };
    }, []);

    return Scaffold(
      appBar: AppBar(
        title: const Text('AEC技术验证'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: Colors.white,
      ),
      backgroundColor: const Color(0xFF1A1A2E),
      body: isInitialized.value
          ? _buildTestInterface(
              context,
              aecService.value!,
              isTestRunning,
              currentTestMode,
              testResults.value,
              audioPlayer,
              playingIndex,
            )
          : _buildLoadingInterface(),
    );
  }

  Widget _buildLoadingInterface() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: Colors.tealAccent),
          SizedBox(height: 16),
          Text(
            '正在初始化AEC测试环境...',
            style: TextStyle(color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildTestInterface(
    BuildContext context,
    ParallelAudioIOService aecService,
    ValueNotifier<bool> isTestRunning,
    ValueNotifier<AECTestMode?> currentTestMode,
    List<AECTestResult> testResults,
    ValueNotifier<AudioPlayer?> audioPlayer,
    ValueNotifier<int?> playingIndex,
  ) {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildServiceStatus(aecService),
                const SizedBox(height: 16),
                _buildTestModes(
                  context,
                  aecService,
                  isTestRunning,
                  currentTestMode,
                ),
                const SizedBox(height: 16),
                _buildTestResultsCompact(testResults),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
        // 将控制按钮固定在底部
        Container(
          padding: const EdgeInsets.all(16.0),
          decoration: const BoxDecoration(
            color: Color(0xFF1A1A2E),
            border: Border(
              top: BorderSide(color: Colors.white12, width: 1),
            ),
          ),
          child: _buildControlButtons(aecService, isTestRunning.value),
        ),
      ],
    );
  }

  Widget _buildServiceStatus(ParallelAudioIOService aecService) {
    return Card(
      color: const Color(0xFF16213E),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '服务状态',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildStatusItem('录音状态', aecService.isRecording ? '录音中' : '空闲'),
            _buildStatusItem('播放状态', aecService.isPlaying ? '播放中' : '空闲'),
            _buildStatusItem(
                '同时模式', aecService.isSimultaneousMode ? '启用' : '禁用'),
            _buildStatusItem('平台', Platform.isIOS ? 'iOS' : 'Android'),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(String label, String value) {
    final isActive = value.contains('中') || value == '启用' || value == 'iOS';
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(color: Colors.white70),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: isActive
                  ? Colors.tealAccent.withValues(alpha: 0.2)
                  : Colors.grey.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              value,
              style: TextStyle(
                color: isActive ? Colors.tealAccent : Colors.white70,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestModes(
    BuildContext context,
    ParallelAudioIOService aecService,
    ValueNotifier<bool> isTestRunning,
    ValueNotifier<AECTestMode?> currentTestMode,
  ) {
    return Card(
      color: const Color(0xFF16213E),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '测试模式',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...AECTestMode.values.map((mode) => _buildTestModeButton(
                  context,
                  mode,
                  aecService,
                  isTestRunning,
                  currentTestMode,
                )),
            const SizedBox(height: 8),
            _buildAECComparisonButton(
              context,
              aecService,
              isTestRunning,
              currentTestMode,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestModeButton(
    BuildContext context,
    AECTestMode mode,
    ParallelAudioIOService aecService,
    ValueNotifier<bool> isTestRunning,
    ValueNotifier<AECTestMode?> currentTestMode,
  ) {
    final isCurrentTest = currentTestMode.value == mode;
    final testInfo = _getTestModeInfo(mode);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: isCurrentTest
            ? Colors.tealAccent.withValues(alpha: 0.2)
            : const Color(0xFF1A1A2E),
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: isTestRunning.value
              ? null
              : () => _runTest(
                    context,
                    mode,
                    aecService,
                    isTestRunning,
                    currentTestMode,
                  ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Icon(
                  testInfo.icon,
                  color: isCurrentTest ? Colors.tealAccent : Colors.white70,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        testInfo.title,
                        style: TextStyle(
                          color:
                              isCurrentTest ? Colors.tealAccent : Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        testInfo.description,
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isCurrentTest && isTestRunning.value)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.tealAccent,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }



  Widget _buildTestResultsCompact(List<AECTestResult> testResults) {
    return Card(
      color: const Color(0xFF16213E),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '测试结果',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '总计: ${testResults.length}',
                  style: const TextStyle(color: Colors.white70),
                ),
              ],
            ),
            const SizedBox(height: 12),
            testResults.isEmpty
                ? const Padding(
                    padding: EdgeInsets.symmetric(vertical: 20),
                    child: Center(
                      child: Text(
                        '暂无测试结果\n点击上方按钮开始测试',
                        textAlign: TextAlign.center,
                        style: TextStyle(color: Colors.white54),
                      ),
                    ),
                  )
                : Column(
                    children: testResults.asMap().entries.map((entry) {
                      final index = entry.key;
                      final result = entry.value;
                      return _buildTestResultItem(result, index + 1);
                    }).toList(),
                  ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestResultItem(AECTestResult result, int index) {
    final testInfo = _getTestModeInfo(result.mode);
    final isSuccess = result.success;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A2E),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSuccess
              ? Colors.tealAccent.withValues(alpha: 0.3)
              : Colors.redAccent.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isSuccess ? Icons.check_circle : Icons.error,
                color: isSuccess ? Colors.tealAccent : Colors.redAccent,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                '测试 $index: ${testInfo.title}',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                '${result.actualDuration?.inSeconds ?? 0}s',
                style: const TextStyle(color: Colors.white70, fontSize: 12),
              ),
            ],
          ),
          if (result.simultaneousSuccess != null) ...[
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  result.simultaneousSuccess!
                      ? Icons.done_all
                      : Icons.sync_problem,
                  color: result.simultaneousSuccess!
                      ? Colors.green
                      : Colors.orange,
                  size: 14,
                ),
                const SizedBox(width: 4),
                Text(
                  '同时播放: ${result.simultaneousSuccess! ? '成功' : '失败'}',
                  style: const TextStyle(color: Colors.white70, fontSize: 12),
                ),
              ],
            ),
          ],
          if (result.errorMessage != null) ...[
            const SizedBox(height: 4),
            Text(
              '错误: ${result.errorMessage}',
              style: const TextStyle(color: Colors.redAccent, fontSize: 11),
            ),
          ],
          if (result.notes.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              result.notes,
              style: const TextStyle(color: Colors.white54, fontSize: 11),
            ),
          ],
          if (result.recordingPath != null) ...[
            const SizedBox(height: 8),
            _buildPlaybackButton(result.recordingPath!),
          ],
        ],
      ),
    );
  }

  /// 构建播放按钮
  Widget _buildPlaybackButton(String recordingPath) {
    return Consumer(
      builder: (context, ref, child) {
        return SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () => _playRecording(context, recordingPath),
            icon: const Icon(Icons.play_arrow, size: 16),
            label: const Text('播放录音', style: TextStyle(fontSize: 12)),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.tealAccent.withValues(alpha: 0.1),
              foregroundColor: Colors.tealAccent,
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              minimumSize: const Size(0, 32),
            ),
          ),
        );
      },
    );
  }

  /// 播放录音文件
  Future<void> _playRecording(BuildContext context, String recordingPath) async {
    try {
      // 检查文件是否存在
      final file = File(recordingPath);
      if (!await file.exists()) {
        throw Exception('录音文件不存在: $recordingPath');
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('开始播放: ${recordingPath.split('/').last}'),
            backgroundColor: Colors.blue,
            duration: const Duration(seconds: 2),
          ),
        );
      }

      // 使用 just_audio 播放录音文件
      final player = AudioPlayer();
      try {
        await player.setFilePath(recordingPath);
        await player.play();

        // 等待播放完成
        await player.playerStateStream
            .where((state) => state.processingState == ProcessingState.completed)
            .first;

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('播放完成'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 1),
            ),
          );
        }
      } finally {
        await player.dispose();
      }

      debugPrint('成功播放录音文件: $recordingPath');

    } catch (e) {
      debugPrint('播放录音失败: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('播放失败: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Widget _buildControlButtons(
      ParallelAudioIOService aecService, bool isTestRunning) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed:
                isTestRunning ? null : () => aecService.clearTestResults(),
            icon: const Icon(Icons.clear_all),
            label: const Text('清除结果'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF16213E),
              foregroundColor: Colors.white70,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: isTestRunning ? null : () => _showTestReport(aecService),
            icon: const Icon(Icons.assessment),
            label: const Text('生成报告'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.tealAccent.withValues(alpha: 0.2),
              foregroundColor: Colors.tealAccent,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _runTest(
    BuildContext context,
    AECTestMode mode,
    ParallelAudioIOService aecService,
    ValueNotifier<bool> isTestRunning,
    ValueNotifier<AECTestMode?> currentTestMode,
  ) async {
    isTestRunning.value = true;
    currentTestMode.value = mode;

    try {
      // 准备测试音频（使用环境音频作为测试音频）
      String? testAudioPath;
      if (mode == AECTestMode.flutterSoundSimultaneous ||
          mode == AECTestMode.flutterSoundWithAEC ||
          mode == AECTestMode.mixedEngines) {
        // 使用内置的环境音频文件
        testAudioPath = 'assets/audio/day_ambient.mp3';
      }

      // 根据不同模式设置合适的测试时长
      Duration testDuration;
      switch (mode) {
        case AECTestMode.flutterSoundSeparate:
          testDuration = const Duration(seconds: 5); // 分离模式较简单
          break;
        case AECTestMode.streamBased:
          testDuration = const Duration(seconds: 6); // 流式处理需要稍长时间
          break;
        case AECTestMode.flutterSoundSimultaneous:
        case AECTestMode.flutterSoundWithAEC:
        case AECTestMode.mixedEngines:
          testDuration = const Duration(seconds: 8); // 复杂模式需要足够时间
          break;
      }

      // 运行测试
      final result = await aecService.startAECTest(
        mode,
        testAudioPath: testAudioPath,
        testDuration: testDuration,
      );

      // 显示测试完成消息
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '测试完成: ${result.success ? "成功" : "失败"}',
            ),
            backgroundColor: result.success ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('测试出错: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      isTestRunning.value = false;
      currentTestMode.value = null;
    }
  }

  void _showTestReport(ParallelAudioIOService aecService) {
    final report = aecService.generateTestReport();
    debugPrint(report);
  }

  /// 构建AEC对比测试按钮
  Widget _buildAECComparisonButton(
    BuildContext context,
    ParallelAudioIOService aecService,
    ValueNotifier<bool> isTestRunning,
    ValueNotifier<AECTestMode?> currentTestMode,
  ) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      child: Material(
        color: const Color(0xFF2E8B57), // 深绿色，表示特殊功能
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: isTestRunning.value
              ? null
              : () => _runAECComparisonTest(
                    context,
                    aecService,
                    isTestRunning,
                    currentTestMode,
                  ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                const Icon(
                  Icons.compare_arrows,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'AEC效果对比测试',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '连续测试：标准录音 vs AEC录音',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.8),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isTestRunning.value)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 运行AEC对比测试
  Future<void> _runAECComparisonTest(
    BuildContext context,
    ParallelAudioIOService aecService,
    ValueNotifier<bool> isTestRunning,
    ValueNotifier<AECTestMode?> currentTestMode,
  ) async {
    isTestRunning.value = true;
    currentTestMode.value = null; // 清除当前模式，表示这是对比测试

    try {
      // 显示测试指导对话框
      final shouldContinue = await _showAECTestInstructions(context);
      if (!shouldContinue) {
        return;
      }

      // 显示开始提示
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('开始AEC对比测试...'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }

      // 运行对比测试
      final results = await aecService.runAECComparisonTest(
        testDuration: const Duration(seconds: 10), // 增加测试时长以获得更好的对比效果
      );

      // 显示完成提示和分析结果
      if (context.mounted) {
        await _showAECTestResults(context, results);
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('对比测试失败: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      isTestRunning.value = false;
      currentTestMode.value = null;
    }
  }

  /// 显示AEC测试指导对话框
  Future<bool> _showAECTestInstructions(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        title: const Text(
          'AEC对比测试指导',
          style: TextStyle(color: Colors.white),
        ),
        content: const SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '本测试将进行两次录音对比：',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text(
                '1. 标准录音（无AEC）\n2. AEC录音（启用回声消除）',
                style: TextStyle(color: Colors.white70),
              ),
              SizedBox(height: 16),
              Text(
                '测试步骤：',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text(
                '• 每次测试时请对着麦克风正常说话\n'
                '• 测试期间会播放背景音乐\n'
                '• 测试完成后可播放录音对比效果\n'
                '• 在AEC录音中，背景音应被显著抑制',
                style: TextStyle(color: Colors.white70),
              ),
              SizedBox(height: 16),
              Text(
                '注意：请确保设备音量适中，环境相对安静',
                style: TextStyle(color: Colors.orange, fontSize: 12),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消', style: TextStyle(color: Colors.grey)),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
            child: const Text('开始测试', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    ) ?? false;
  }

  /// 显示AEC测试结果分析
  Future<void> _showAECTestResults(BuildContext context, List<AECTestResult> results) async {
    if (results.length < 2) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('测试结果不完整'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final standardTest = results[0];
    final aecTest = results[1];

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        title: const Text(
          'AEC对比测试结果',
          style: TextStyle(color: Colors.white),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTestResultSummary('标准录音（无AEC）', standardTest),
              const SizedBox(height: 16),
              _buildTestResultSummary('AEC录音', aecTest),
              const SizedBox(height: 16),
              const Text(
                '验证方法：',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text(
                '• 播放两个录音文件进行对比\n'
                '• AEC录音中的背景音应明显减少\n'
                '• 您的语音在两个录音中都应清晰',
                style: TextStyle(color: Colors.white70),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭', style: TextStyle(color: Colors.grey)),
          ),
        ],
      ),
    );
  }

  /// 构建测试结果摘要
  Widget _buildTestResultSummary(String title, AECTestResult result) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF0F0F23),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: result.success ? Colors.green : Colors.red,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                result.success ? Icons.check_circle : Icons.error,
                color: result.success ? Colors.green : Colors.red,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '状态: ${result.success ? "成功" : "失败"}',
            style: const TextStyle(color: Colors.white70, fontSize: 12),
          ),
          if (result.recordingPath != null) ...[
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _playRecording(context, result.recordingPath!),
                icon: const Icon(Icons.play_arrow, size: 16),
                label: const Text('播放此录音', style: TextStyle(fontSize: 12)),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.tealAccent.withValues(alpha: 0.1),
                  foregroundColor: Colors.tealAccent,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  TestModeInfo _getTestModeInfo(AECTestMode mode) {
    switch (mode) {
      case AECTestMode.flutterSoundSeparate:
        return TestModeInfo(
          title: '分离模式',
          description: 'FlutterSound先录音后播放，验证基础功能',
          icon: Icons.schedule,
        );
      case AECTestMode.flutterSoundSimultaneous:
        return TestModeInfo(
          title: '同时模式',
          description: 'FlutterSound同时录音播放，测试冲突',
          icon: Icons.sync,
        );
      case AECTestMode.flutterSoundWithAEC:
        return TestModeInfo(
          title: 'iOS AEC',
          description: 'FlutterSound启用enableVoiceProcessing（仅iOS）',
          icon: Icons.hearing,
        );
      case AECTestMode.mixedEngines:
        return TestModeInfo(
          title: '混合引擎',
          description: 'FlutterSound录音 + JustAudio播放',
          icon: Icons.merge_type,
        );
      case AECTestMode.streamBased:
        return TestModeInfo(
          title: '流式处理',
          description: 'FlutterSound流式录音，实时音频处理',
          icon: Icons.stream,
        );
    }
  }
}

class TestModeInfo {
  final String title;
  final String description;
  final IconData icon;

  TestModeInfo({
    required this.title,
    required this.description,
    required this.icon,
  });
}
