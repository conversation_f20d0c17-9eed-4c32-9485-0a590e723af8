/// AI情绪分析器单元测试

import 'package:flutter_test/flutter_test.dart';
import 'package:echo_cave/services/resonance/ai_emotion_analyzer.dart';
import 'package:echo_cave/services/resonance/resonance_models.dart';

void main() {
  group('AiEmotionAnalyzer Tests', () {
    group('Emotion Analysis', () {
      test('should handle empty text gracefully', () async {
        final result = await AiEmotionAnalyzer.analyzeEmotion(
          '',
          EmotionTendency.negative,
        );

        expect(result, isNull);
      });

      test('should handle whitespace-only text gracefully', () async {
        final result = await AiEmotionAnalyzer.analyzeEmotion(
          '   \n\t  ',
          EmotionTendency.positive,
        );

        expect(result, isNull);
      });

      // Note: These tests will only pass if DeepSeek API key is configured
      // In CI/CD or environments without API key, these tests will be skipped
      test('should analyze negative emotion text (requires API key)', () async {
        final result = await AiEmotionAnalyzer.analyzeEmotion(
          '我今天很难过，感觉很痛苦',
          EmotionTendency.negative,
        );

        if (result != null) {
          expect(result.tendency, EmotionTendency.negative);
          expect(result.confidence, greaterThan(0.5));
          expect(result.reasoning, isNotEmpty);
          expect(result.detectedEmotions, isNotEmpty);
        } else {
          // API key not configured, test skipped
          print('Skipping AI emotion analysis test - API key not configured');
        }
      }, skip: 'Requires DeepSeek API key configuration');

      test('should analyze positive emotion text (requires API key)', () async {
        final result = await AiEmotionAnalyzer.analyzeEmotion(
          '我今天很开心，感觉很棒',
          EmotionTendency.positive,
        );

        if (result != null) {
          expect(result.tendency, EmotionTendency.positive);
          expect(result.confidence, greaterThan(0.5));
          expect(result.reasoning, isNotEmpty);
          expect(result.detectedEmotions, isNotEmpty);
        } else {
          print('Skipping AI emotion analysis test - API key not configured');
        }
      }, skip: 'Requires DeepSeek API key configuration');
    });

    group('Semantic Analysis', () {
      test('should handle empty text gracefully', () async {
        final result = await AiEmotionAnalyzer.analyzeSemanticMatch(
          '',
          ['疲惫', '累'],
        );

        expect(result, isNull);
      });

      test('should handle empty concepts gracefully', () async {
        final result = await AiEmotionAnalyzer.analyzeSemanticMatch(
          '我很累',
          [],
        );

        expect(result, isNull);
      });

      test('should analyze semantic match (requires API key)', () async {
        final result = await AiEmotionAnalyzer.analyzeSemanticMatch(
          '我感觉筋疲力尽了',
          ['疲惫', '累'],
        );

        if (result != null) {
          expect(result.isMatch, true);
          expect(result.confidence, greaterThan(0.5));
          expect(result.reasoning, isNotEmpty);
          expect(result.matchedConcepts, isNotEmpty);
        } else {
          print('Skipping AI semantic analysis test - API key not configured');
        }
      }, skip: 'Requires DeepSeek API key configuration');

      test('should not match unrelated concepts (requires API key)', () async {
        final result = await AiEmotionAnalyzer.analyzeSemanticMatch(
          '今天天气很好',
          ['疲惫', '累'],
        );

        if (result != null) {
          expect(result.isMatch, false);
          expect(result.confidence, lessThan(0.5));
        } else {
          print('Skipping AI semantic analysis test - API key not configured');
        }
      }, skip: 'Requires DeepSeek API key configuration');
    });

    group('Caching', () {
      test('should cache emotion analysis results', () async {
        // Clear cache first
        AiEmotionAnalyzer.clearCache();
        
        final initialStats = AiEmotionAnalyzer.getCacheStats();
        expect(initialStats['emotionCacheSize'], 0);

        // This will either add to cache (if API key available) or remain empty
        await AiEmotionAnalyzer.analyzeEmotion(
          '测试文本',
          EmotionTendency.neutral,
        );

        final finalStats = AiEmotionAnalyzer.getCacheStats();
        // Cache size should be 0 or 1 depending on API availability
        expect(finalStats['emotionCacheSize'], lessThanOrEqualTo(1));
      });

      test('should cache semantic analysis results', () async {
        // Clear cache first
        AiEmotionAnalyzer.clearCache();
        
        final initialStats = AiEmotionAnalyzer.getCacheStats();
        expect(initialStats['semanticCacheSize'], 0);

        // This will either add to cache (if API key available) or remain empty
        await AiEmotionAnalyzer.analyzeSemanticMatch(
          '测试文本',
          ['测试'],
        );

        final finalStats = AiEmotionAnalyzer.getCacheStats();
        // Cache size should be 0 or 1 depending on API availability
        expect(finalStats['semanticCacheSize'], lessThanOrEqualTo(1));
      });

      test('should clear cache correctly', () {
        AiEmotionAnalyzer.clearCache();
        
        final stats = AiEmotionAnalyzer.getCacheStats();
        expect(stats['emotionCacheSize'], 0);
        expect(stats['semanticCacheSize'], 0);
      });
    });

    group('Result Models', () {
      test('should create AiEmotionResult from JSON', () {
        final json = {
          'tendency': 'negative',
          'confidence': 0.8,
          'reasoning': 'Test reasoning',
          'detectedEmotions': ['sad', 'tired'],
          'metadata': {'intensity': 'high'},
        };

        final result = AiEmotionResult.fromJson(json);

        expect(result.tendency, EmotionTendency.negative);
        expect(result.confidence, 0.8);
        expect(result.reasoning, 'Test reasoning');
        expect(result.detectedEmotions, ['sad', 'tired']);
        expect(result.metadata['intensity'], 'high');
      });

      test('should create AiSemanticResult from JSON', () {
        final json = {
          'isMatch': true,
          'confidence': 0.9,
          'reasoning': 'Test reasoning',
          'matchedConcepts': ['concept1', 'concept2'],
        };

        final result = AiSemanticResult.fromJson(json);

        expect(result.isMatch, true);
        expect(result.confidence, 0.9);
        expect(result.reasoning, 'Test reasoning');
        expect(result.matchedConcepts, ['concept1', 'concept2']);
      });

      test('should handle invalid emotion tendency gracefully', () {
        final json = {
          'tendency': 'invalid_tendency',
          'confidence': 0.5,
          'reasoning': 'Test',
          'detectedEmotions': [],
        };

        final result = AiEmotionResult.fromJson(json);

        expect(result.tendency, EmotionTendency.neutral); // Should default to neutral
      });
    });
  });
}
