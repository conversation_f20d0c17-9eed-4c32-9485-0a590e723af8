/// 智能回应选择器
/// 负责根据上下文、历史和用户状态选择最合适的回应

import 'dart:math';
import 'package:flutter/foundation.dart';
import 'resonance_models.dart';
import 'resonance_engine_service.dart';

/// 回应选择策略
enum ResponseSelectionStrategy {
  /// 随机选择
  random,
  
  /// 基于上下文的智能选择
  contextAware,
  
  /// 基于情绪强度的选择
  emotionBased,
  
  /// 避免重复的选择
  antiRepetitive,
}

/// 回应选择结果
class ResponseSelectionResult {
  final String selectedResponse;
  final double confidence;
  final ResponseSelectionStrategy strategy;
  final Map<String, dynamic> metadata;

  const ResponseSelectionResult({
    required this.selectedResponse,
    required this.confidence,
    required this.strategy,
    this.metadata = const {},
  });
}

/// 智能回应选择器
class ResponseSelector {
  static const int _maxHistorySize = 20;
  static final Random _random = Random();
  
  // 回应历史记录
  static final List<String> _responseHistory = [];
  
  // 上下文分析缓存
  static final Map<String, double> _contextScores = {};

  /// 选择最合适的回应
  static ResponseSelectionResult selectResponse({
    required ResonanceRule rule,
    required List<ConversationContextItem> context,
    ResponseSelectionStrategy strategy = ResponseSelectionStrategy.contextAware,
    Map<String, dynamic>? additionalMetadata,
  }) {
    final responsePool = rule.action.responsePool;
    
    if (responsePool.isEmpty) {
      return ResponseSelectionResult(
        selectedResponse: '嗯',
        confidence: 0.5,
        strategy: ResponseSelectionStrategy.random,
        metadata: {'fallback': true},
      );
    }

    switch (strategy) {
      case ResponseSelectionStrategy.random:
        return _selectRandom(responsePool, rule);
      
      case ResponseSelectionStrategy.contextAware:
        return _selectContextAware(responsePool, rule, context, additionalMetadata);
      
      case ResponseSelectionStrategy.emotionBased:
        return _selectEmotionBased(responsePool, rule, context, additionalMetadata);
      
      case ResponseSelectionStrategy.antiRepetitive:
        return _selectAntiRepetitive(responsePool, rule);
    }
  }

  /// 随机选择
  static ResponseSelectionResult _selectRandom(
    List<String> responsePool,
    ResonanceRule rule,
  ) {
    final selected = responsePool[_random.nextInt(responsePool.length)];
    _addToHistory(selected);
    
    return ResponseSelectionResult(
      selectedResponse: selected,
      confidence: 0.7,
      strategy: ResponseSelectionStrategy.random,
      metadata: {
        'poolSize': responsePool.length,
        'ruleId': rule.id,
      },
    );
  }

  /// 基于上下文的智能选择
  static ResponseSelectionResult _selectContextAware(
    List<String> responsePool,
    ResonanceRule rule,
    List<ConversationContextItem> context,
    Map<String, dynamic>? additionalMetadata,
  ) {
    // 分析上下文情绪强度
    final emotionIntensity = _analyzeEmotionIntensity(context);
    
    // 分析对话长度
    final conversationLength = _analyzeConversationLength(context);
    
    // 根据规则类型和上下文选择合适的回应
    String selectedResponse;
    double confidence = 0.8;
    final metadata = <String, dynamic>{
      'emotionIntensity': emotionIntensity,
      'conversationLength': conversationLength,
      'ruleType': rule.condition.type.name,
      'actionType': rule.action.type.name,
    };

    if (additionalMetadata != null) {
      metadata.addAll(additionalMetadata);
    }

    // 根据情绪强度选择回应强度
    if (emotionIntensity > 0.8) {
      // 高情绪强度：选择更有同理心的回应
      selectedResponse = _selectByEmotionLevel(responsePool, 'high');
      confidence = 0.9;
      metadata['selectionReason'] = 'high_emotion_intensity';
    } else if (emotionIntensity > 0.5) {
      // 中等情绪强度：选择平衡的回应
      selectedResponse = _selectByEmotionLevel(responsePool, 'medium');
      confidence = 0.8;
      metadata['selectionReason'] = 'medium_emotion_intensity';
    } else {
      // 低情绪强度：选择简洁的回应
      selectedResponse = _selectByEmotionLevel(responsePool, 'low');
      confidence = 0.7;
      metadata['selectionReason'] = 'low_emotion_intensity';
    }

    // 避免重复最近的回应
    if (_responseHistory.contains(selectedResponse) && responsePool.length > 1) {
      final alternatives = responsePool.where((r) => !_responseHistory.contains(r)).toList();
      if (alternatives.isNotEmpty) {
        selectedResponse = alternatives[_random.nextInt(alternatives.length)];
        metadata['avoidedRepetition'] = true;
      }
    }

    _addToHistory(selectedResponse);
    
    return ResponseSelectionResult(
      selectedResponse: selectedResponse,
      confidence: confidence,
      strategy: ResponseSelectionStrategy.contextAware,
      metadata: metadata,
    );
  }

  /// 基于情绪的选择
  static ResponseSelectionResult _selectEmotionBased(
    List<String> responsePool,
    ResonanceRule rule,
    List<ConversationContextItem> context,
    Map<String, dynamic>? additionalMetadata,
  ) {
    // 分析最近文本的情绪关键词
    final recentText = context
        .where((item) => item.isFinal)
        .map((item) => item.text)
        .take(2)
        .join(' ');

    final emotionKeywords = _detectEmotionKeywords(recentText);
    final selectedResponse = _selectByEmotionKeywords(responsePool, emotionKeywords);
    
    _addToHistory(selectedResponse);
    
    return ResponseSelectionResult(
      selectedResponse: selectedResponse,
      confidence: 0.85,
      strategy: ResponseSelectionStrategy.emotionBased,
      metadata: {
        'detectedEmotions': emotionKeywords,
        'analyzedText': recentText,
        'ruleId': rule.id,
        ...?additionalMetadata,
      },
    );
  }

  /// 避免重复的选择
  static ResponseSelectionResult _selectAntiRepetitive(
    List<String> responsePool,
    ResonanceRule rule,
  ) {
    // 优先选择最近没有使用过的回应
    final availableResponses = responsePool
        .where((response) => !_responseHistory.contains(response))
        .toList();

    String selectedResponse;
    if (availableResponses.isNotEmpty) {
      selectedResponse = availableResponses[_random.nextInt(availableResponses.length)];
    } else {
      // 如果所有回应都用过了，选择使用时间最久的
      selectedResponse = responsePool[_random.nextInt(responsePool.length)];
    }

    _addToHistory(selectedResponse);
    
    return ResponseSelectionResult(
      selectedResponse: selectedResponse,
      confidence: 0.75,
      strategy: ResponseSelectionStrategy.antiRepetitive,
      metadata: {
        'availableCount': availableResponses.length,
        'totalCount': responsePool.length,
        'historySize': _responseHistory.length,
      },
    );
  }

  /// 分析情绪强度
  static double _analyzeEmotionIntensity(List<ConversationContextItem> context) {
    if (context.isEmpty) return 0.0;

    final recentTexts = context
        .where((item) => item.isFinal)
        .map((item) => item.text)
        .take(3)
        .join(' ')
        .toLowerCase();

    // 强情绪词汇
    final strongEmotionWords = ['崩溃', '绝望', '痛苦', '愤怒', '恐惧', '兴奋', '狂欢'];
    final mediumEmotionWords = ['难过', '开心', '担心', '紧张', '满足', '失望'];
    final mildEmotionWords = ['累', '烦', '好', '不错', '还行', '一般'];

    double intensity = 0.0;
    
    for (final word in strongEmotionWords) {
      if (recentTexts.contains(word)) intensity += 0.3;
    }
    
    for (final word in mediumEmotionWords) {
      if (recentTexts.contains(word)) intensity += 0.2;
    }
    
    for (final word in mildEmotionWords) {
      if (recentTexts.contains(word)) intensity += 0.1;
    }

    return min(1.0, intensity);
  }

  /// 分析对话长度
  static int _analyzeConversationLength(List<ConversationContextItem> context) {
    return context.where((item) => item.isFinal).length;
  }

  /// 根据情绪级别选择回应
  static String _selectByEmotionLevel(List<String> responsePool, String level) {
    // 根据回应长度和内容特征进行分类
    final shortResponses = responsePool.where((r) => r.length <= 3).toList();
    final mediumResponses = responsePool.where((r) => r.length > 3 && r.length <= 8).toList();
    final longResponses = responsePool.where((r) => r.length > 8).toList();

    switch (level) {
      case 'high':
        // 高情绪：优先选择较长、更有同理心的回应
        if (longResponses.isNotEmpty) {
          return longResponses[_random.nextInt(longResponses.length)];
        }
        if (mediumResponses.isNotEmpty) {
          return mediumResponses[_random.nextInt(mediumResponses.length)];
        }
        break;
      
      case 'medium':
        // 中等情绪：优先选择中等长度的回应
        if (mediumResponses.isNotEmpty) {
          return mediumResponses[_random.nextInt(mediumResponses.length)];
        }
        break;
      
      case 'low':
        // 低情绪：优先选择简短的回应
        if (shortResponses.isNotEmpty) {
          return shortResponses[_random.nextInt(shortResponses.length)];
        }
        break;
    }

    // 回退到随机选择
    return responsePool[_random.nextInt(responsePool.length)];
  }

  /// 检测情绪关键词
  static List<String> _detectEmotionKeywords(String text) {
    final keywords = <String>[];
    final lowerText = text.toLowerCase();

    final emotionMap = {
      'sadness': ['难过', '伤心', '痛苦', '哭'],
      'anger': ['生气', '愤怒', '烦', '气'],
      'fear': ['害怕', '恐惧', '担心', '焦虑'],
      'joy': ['开心', '高兴', '快乐', '兴奋'],
      'fatigue': ['累', '疲惫', '筋疲力尽'],
    };

    for (final entry in emotionMap.entries) {
      for (final word in entry.value) {
        if (lowerText.contains(word)) {
          keywords.add(entry.key);
          break;
        }
      }
    }

    return keywords;
  }

  /// 根据情绪关键词选择回应
  static String _selectByEmotionKeywords(List<String> responsePool, List<String> emotions) {
    // 这里可以根据检测到的情绪类型选择更合适的回应
    // 目前简化为随机选择，但保留了扩展的可能性
    return responsePool[_random.nextInt(responsePool.length)];
  }

  /// 添加到历史记录
  static void _addToHistory(String response) {
    _responseHistory.add(response);
    if (_responseHistory.length > _maxHistorySize) {
      _responseHistory.removeAt(0);
    }
  }

  /// 清除历史记录
  static void clearHistory() {
    _responseHistory.clear();
    _contextScores.clear();
  }

  /// 获取历史统计
  static Map<String, dynamic> getHistoryStats() {
    return {
      'historySize': _responseHistory.length,
      'recentResponses': _responseHistory.take(5).toList(),
      'contextScoreCount': _contextScores.length,
    };
  }
}
