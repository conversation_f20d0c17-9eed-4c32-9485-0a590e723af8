import 'package:echo_cave/models/recording_model.dart';
import 'package:echo_cave/services/ai_tagging_service.dart';
import 'package:echo_cave/services/ambient_sound_service.dart';
import 'package:echo_cave/services/audio_recording_service.dart';
import 'package:echo_cave/services/encryption_service.dart';
import 'package:echo_cave/data/secure_storage_service.dart';
import 'package:echo_cave/widgets/waveform_painter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:echo_cave/views/tts_test_view.dart';
import 'package:echo_cave/views/conversation_test_view.dart';
import 'package:echo_cave/views/aec_test_view.dart';
import 'package:echo_cave/views/stt_test_view.dart';
import 'package:echo_cave/views/empathy_test_view.dart';
import 'package:echo_cave/views/immersive_listening_view.dart';
import 'package:echo_cave/views/cost_optimized_stt_test_view.dart';
import 'package:echo_cave/views/aec_stt_test_view.dart';

final ambientSoundServiceProvider =
    ChangeNotifierProvider((ref) => AmbientSoundService());

final aiTaggingServiceProvider =
    Provider((ref) => AITaggingService(const FlutterSecureStorage()));

final audioRecordingServiceProvider =
    ChangeNotifierProvider((ref) => AudioRecordingServiceNotifier());

final encryptionServiceProvider = Provider((ref) => EncryptionService());

final secureStorageServiceProvider =
    Provider((ref) => SecureStorageService(const FlutterSecureStorage()));

class MainView extends ConsumerStatefulWidget {
  const MainView({super.key});

  @override
  ConsumerState<MainView> createState() => _MainViewState();
}

class _MainViewState extends ConsumerState<MainView>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  // State variables
  List<Recording> recordings = [];
  bool isDragging = false;
  double dbLevel = 0.0;

  // Animation controllers
  late AnimationController breathingAnimationController;
  late AnimationController glowAnimationController;
  late Animation<double> breathingAnimation;
  late Animation<double> glowAnimation;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Initialize animation controllers
    breathingAnimationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);

    glowAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    // Initialize animations
    breathingAnimation = Tween<double>(begin: 0.95, end: 1.05).animate(
      CurvedAnimation(
        parent: breathingAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    glowAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: glowAnimationController,
        curve: Curves.easeOut,
      ),
    );

    // Start ambient sound
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startAmbientSound();
    });
  }

  void _startAmbientSound() {
    final hour = DateTime.now().hour;
    final bool isDayTime = hour >= 6 && hour < 19;
    final soundService = ref.read(ambientSoundServiceProvider);
    soundService.setTime(isDayTime);
  }

  void _stopAmbientSound() {
    final soundService = ref.read(ambientSoundServiceProvider);
    soundService.stop();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.detached:
        _stopAmbientSound();
        break;
      case AppLifecycleState.resumed:
        _startAmbientSound();
        break;
      case AppLifecycleState.inactive:
        // Do nothing for inactive state
        break;
      case AppLifecycleState.hidden:
        _stopAmbientSound();
        break;
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);

    // Stop the ambient sound when leaving MainView
    _stopAmbientSound();

    // Dispose animation controllers
    breathingAnimationController.dispose();
    glowAnimationController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Services and State
    final audioService = ref.watch(audioRecordingServiceProvider);
    final soundService = ref.watch(ambientSoundServiceProvider);
    final aiTaggingService = ref.read(aiTaggingServiceProvider);
    final encryptionService = ref.read(encryptionServiceProvider);
    final storageService = ref.read(secureStorageServiceProvider);
    final isRecording = audioService.isRecording;

    // Update glow animation based on recording state
    if (isRecording) {
      glowAnimationController.forward();
    } else {
      glowAnimationController.reverse();
    }

    // Day/Night Cycle
    final hour = DateTime.now().hour;
    final bool isDayTime = hour >= 6 && hour < 19;

    // Local Function for Playback
    Future<void> playRecording(Recording recording) async {
      if (audioService.isPlaying) {
        await audioService.stopPlayer();
      } else {
        final key = await storageService.getEncryptionKey();
        if (key == null) {
          print("Fatal: Couldn't get encryption key for playback.");
          return;
        }

        final tempDir = await getTemporaryDirectory();
        final decryptedPath =
            '${tempDir.path}/${recording.filePath.split('/').last.replaceFirst('_encrypted', '_decrypted')}';

        final result = await encryptionService.decryptFile(
            recording.filePath, decryptedPath, key);

        if (result.success) {
          print('File decrypted for playback: ${result.outputFilePath}');
          await audioService.play(result.outputFilePath!);
        } else {
          print('Decryption for playback failed: ${result.errorMessage}');
        }
      }
    }

    return Scaffold(
      body: Stack(
        fit: StackFit.expand,
        children: [
          // 1. Animated Background
          AnimatedSwitcher(
            duration: const Duration(seconds: 2),
            child: isDayTime
                ? Stack(
                    key: const ValueKey('day'),
                    fit: StackFit.expand,
                    children: [
                      Image.asset(
                        'assets/images/cave_background_v2.png',
                        fit: BoxFit.cover,
                      ),
                    ],
                  )
                : Stack(
                    key: const ValueKey('night'),
                    fit: StackFit.expand,
                    children: [
                      Image.asset(
                        'assets/images/cave_background_v2.png',
                        fit: BoxFit.cover,
                      ),
                      Container(color: Colors.black.withOpacity(0.6)),
                    ],
                  ),
          ),

          // 2. Main Content
          Center(
            child: Stack(
              alignment: Alignment.center,
              children: [
                CustomPaint(
                  size: const Size(300, 300),
                  painter:
                      WaveformPainter(dbLevel: isRecording ? dbLevel : 0.0),
                ),
                DragTarget<Recording>(
                  builder: (context, candidateData, rejectedData) {
                    final isHovering = candidateData.isNotEmpty;
                    return GestureDetector(
                      onTap: () async {
                        // 树洞核心交互：点击开始/停止录音
                        if (audioService.isRecording) {
                          // 如果正在录音，停止录音
                          await audioService.stopRecording();
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('录音已停止并保存到树洞'),
                              backgroundColor: Colors.green,
                              duration: Duration(seconds: 2),
                            ),
                          );
                        } else {
                          // 如果没有录音，开始录音
                          // 停止环境音效
                          final soundService = ref.read(ambientSoundServiceProvider);
                          soundService.stop();

                          await audioService.startRecording();
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('开始倾诉...'),
                              backgroundColor: Colors.blue,
                              duration: Duration(seconds: 2),
                            ),
                          );
                        }
                      },
                      onLongPress: () {
                        // 长按显示模式选择面板（保留原有功能）
                        showModalBottomSheet(
                          context: context,
                          builder: (ctx) => const ModeSelectionPanel(),
                        );
                      },
                      child: ScaleTransition(
                        scale: breathingAnimation,
                        child: AnimatedBuilder(
                          animation: glowAnimation,
                          builder: (context, child) {
                            return Container(
                              width: 150,
                              height: 150,
                              decoration: BoxDecoration(
                                color: Colors.black.withOpacity(0.8),
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: const Color(0xFFC2A53C)
                                      .withOpacity(isHovering ? 1.0 : 0.7),
                                  width: isHovering ? 6 : 4,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: isHovering
                                        ? Colors.red.withOpacity(0.8)
                                        : Color.lerp(
                                            const Color(0xFFC2A53C)
                                                .withOpacity(0.5),
                                            Colors.red.withOpacity(0.7),
                                            glowAnimation.value,
                                          )!,
                                    blurRadius: isHovering
                                        ? 40.0
                                        : 25.0 + (15 * glowAnimation.value),
                                    spreadRadius: isHovering
                                        ? 10.0
                                        : 4.0 + (5 * glowAnimation.value),
                                  ),
                                ],
                              ),
                              child: child,
                            );
                          },
                          child: Icon(
                            isHovering
                                ? Icons.delete_forever
                                : (isRecording
                                    ? Icons.stop_rounded
                                    : Icons.multitrack_audio_rounded),
                            color: Colors.white.withOpacity(0.8),
                            size: 70,
                          ),
                        ),
                      ),
                    );
                  },
                  onWillAccept: (data) => true,
                  onAccept: (recording) async {
                    // Create a new list without the deleted recording for the UI
                    final updatedList =
                        recordings.where((r) => r.id != recording.id).toList();
                    setState(() {
                      recordings = updatedList;
                    });

                    // Now, physically delete the file from storage
                    try {
                      final fileToDelete = File(recording.filePath);
                      if (await fileToDelete.exists()) {
                        await fileToDelete.delete();
                        print(
                            'Successfully deleted file: ${recording.filePath}');
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('The echo has faded away forever...'),
                            backgroundColor: Colors.black87,
                          ),
                        );
                      } else {
                        print(
                            'File to delete not found: ${recording.filePath}');
                      }
                    } catch (e) {
                      print('Error deleting file: $e');
                      // Optionally, show an error message to the user
                    }
                  },
                ),
              ],
            ),
          ),

          // 3. Recordings List
          Positioned(
            bottom: 30,
            left: 20,
            right: 20,
            child: SizedBox(
              height: 150,
              child: ListView.builder(
                physics: isDragging
                    ? const NeverScrollableScrollPhysics()
                    : const BouncingScrollPhysics(),
                itemCount: recordings.length,
                itemBuilder: (context, index) {
                  final recording = recordings[index];

                  return LongPressDraggable<Recording>(
                    data: recording,
                    onDragStarted: () => setState(() => isDragging = true),
                    onDragEnd: (details) => setState(() => isDragging = false),
                    onDraggableCanceled: (velocity, offset) =>
                        setState(() => isDragging = false),
                    feedback: Material(
                      color: Colors.transparent,
                      elevation: 4.0,
                      child: ConstrainedBox(
                        constraints: BoxConstraints(
                          maxWidth: MediaQuery.of(context).size.width * 0.9,
                        ),
                        child: RecordingItem(
                          recording: recording,
                          onTap: () {},
                        ),
                      ),
                    ),
                    childWhenDragging: Opacity(
                      opacity: 0.4,
                      child: RecordingItem(
                        recording: recording,
                        onTap: () {},
                      ),
                    ),
                    child: RecordingItem(
                      recording: recording,
                      onTap: () => playRecording(recording),
                      isAwaitingTagging: recording.isTagging,
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class RecordingItem extends StatelessWidget {
  final Recording recording;
  final VoidCallback onTap;
  final bool isAwaitingTagging;

  const RecordingItem({
    super.key,
    required this.recording,
    required this.onTap,
    this.isAwaitingTagging = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.black.withOpacity(0.5),
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 16),
      child: ListTile(
        leading: const Icon(Icons.graphic_eq_rounded, color: Colors.white70),
        title: _buildTitle(),
        subtitle: _buildSubtitle(),
        onTap: onTap,
        contentPadding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      ),
    );
  }

  Widget _buildTitle() {
    if (isAwaitingTagging) {
      return const Row(
        children: [
          Text(
            'Analyzing for tags...',
            style: TextStyle(
              color: Colors.white,
              fontStyle: FontStyle.italic,
            ),
          ),
          SizedBox(width: 8),
          SizedBox(
            height: 12,
            width: 12,
            child:
                CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
          ),
        ],
      );
    }
    return Text(
      recording.title.isEmpty ? 'Secret #${recording.id}' : recording.title,
      style: const TextStyle(
        color: Colors.white,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildSubtitle() {
    if (recording.tags.isEmpty) {
      final date = DateTime.fromMillisecondsSinceEpoch(recording.timestamp);
      final formattedDate =
          '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      return Text(
        formattedDate,
        style: const TextStyle(color: Colors.white70),
        overflow: TextOverflow.ellipsis,
      );
    }
    return Text(
      recording.tags.join(', '),
      style:
          TextStyle(color: Colors.amber.shade300, fontStyle: FontStyle.italic),
      overflow: TextOverflow.ellipsis,
    );
  }
}

class ModeSelectionPanel extends ConsumerWidget {
  const ModeSelectionPanel({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SafeArea(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '选择模式',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 24),

            // 核心功能
            Row(
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: ElevatedButton.icon(
                      icon: const Icon(Icons.mic, size: 20),
                      label: const Text('🎤 快速记录', style: TextStyle(fontSize: 12)),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      onPressed: () async {
                        Navigator.of(context).pop();

                        // 获取录音服务
                        final audioService = ref.read(audioRecordingServiceProvider);

                        if (audioService.isRecording) {
                          // 如果正在录音，停止录音
                          await audioService.stopRecording();

                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('录音已停止并保存到树洞'),
                              backgroundColor: Colors.green,
                              duration: Duration(seconds: 2),
                            ),
                          );
                        } else {
                          // 如果没有录音，开始录音
                          await audioService.startRecording();

                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('开始倾诉...'),
                              backgroundColor: Colors.blue,
                              duration: Duration(seconds: 2),
                            ),
                          );
                        }
                      },
                    ),
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: Consumer(
                      builder: (context, ref, child) {
                        return ElevatedButton.icon(
                          icon: const Icon(Icons.smart_toy, size: 20),
                          label: const Text('🤖 AI陪伴', style: TextStyle(fontSize: 12)),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.deepPurple,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                          onPressed: () {
                            Navigator.of(context).pop();
                            final soundService = ref.read(ambientSoundServiceProvider);
                            soundService.stop();
                            Navigator.push(
                              context,
                              MaterialPageRoute(builder: (_) => const ImmersiveListeningView()),
                            );
                          },
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 开发者模式按钮
            Center(
              child: TextButton.icon(
                icon: const Icon(Icons.developer_mode, size: 16, color: Colors.grey),
                label: const Text(
                  '开发者模式',
                  style: TextStyle(fontSize: 11, color: Colors.grey),
                ),
                onPressed: () => _showDeveloperOptions(context, ref),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示开发者选项
  void _showDeveloperOptions(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        title: const Text(
          '开发者选项',
          style: TextStyle(color: Colors.white),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // AEC测试
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.hearing, size: 18),
                  label: const Text('🔊 AEC测试'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                  ),
                  onPressed: () {
                    Navigator.of(context).pop(); // 关闭开发者选项对话框
                    Navigator.of(context).pop(); // 关闭模式选择面板
                    final soundService = ref.read(ambientSoundServiceProvider);
                    soundService.stop();
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => DeveloperTestWrapper(
                          child: const AECTestView(),
                          onBack: () => _showDeveloperOptions(context, ref),
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 8),

              // STT测试
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.record_voice_over, size: 18),
                  label: const Text('🎤 STT测试'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal,
                    foregroundColor: Colors.white,
                  ),
                  onPressed: () {
                    Navigator.of(context).pop(); // 关闭开发者选项对话框
                    Navigator.of(context).pop(); // 关闭模式选择面板
                    final soundService = ref.read(ambientSoundServiceProvider);
                    soundService.stop();
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => DeveloperTestWrapper(
                          child: const SttTestView(),
                          onBack: () => _showDeveloperOptions(context, ref),
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 8),

              // 共鸣测试
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.favorite, size: 18),
                  label: const Text('💝 共鸣测试'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.pink,
                    foregroundColor: Colors.white,
                  ),
                  onPressed: () {
                    Navigator.of(context).pop(); // 关闭开发者选项对话框
                    Navigator.of(context).pop(); // 关闭模式选择面板
                    final soundService = ref.read(ambientSoundServiceProvider);
                    soundService.stop();
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => DeveloperTestWrapper(
                          child: const EmpathyTestView(),
                          onBack: () => _showDeveloperOptions(context, ref),
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 8),

              // 成本优化测试
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.savings, size: 18),
                  label: const Text('💰 成本优化'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                  onPressed: () {
                    Navigator.of(context).pop(); // 关闭开发者选项对话框
                    Navigator.of(context).pop(); // 关闭模式选择面板
                    final soundService = ref.read(ambientSoundServiceProvider);
                    soundService.stop();
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => DeveloperTestWrapper(
                          child: const CostOptimizedSttTestView(),
                          onBack: () => _showDeveloperOptions(context, ref),
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 8),

              // 流式STT测试
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.integration_instructions, size: 18),
                  label: const Text('🔗 流式STT'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.deepOrange,
                    foregroundColor: Colors.white,
                  ),
                  onPressed: () {
                    Navigator.of(context).pop(); // 关闭开发者选项对话框
                    Navigator.of(context).pop(); // 关闭模式选择面板
                    final soundService = ref.read(ambientSoundServiceProvider);
                    soundService.stop();
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => DeveloperTestWrapper(
                          child: const AecSttTestView(),
                          onBack: () => _showDeveloperOptions(context, ref),
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 8),

              // Task 24 共鸣引擎演示
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.psychology, size: 18),
                  label: const Text('🧠 Task24 共鸣引擎'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.deepPurple,
                    foregroundColor: Colors.white,
                  ),
                  onPressed: () {
                    Navigator.of(context).pop(); // 关闭开发者选项对话框
                    Navigator.of(context).pop(); // 关闭模式选择面板
                    final soundService = ref.read(ambientSoundServiceProvider);
                    soundService.stop();
                    Navigator.pushNamed(context, '/resonance-demo');
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭', style: TextStyle(color: Colors.grey)),
          ),
        ],
      ),
    );
  }

  /// 显示沉浸式倾听界面
  void _showImmersiveListening(BuildContext context, WidgetRef ref) {
    final soundService = ref.read(ambientSoundServiceProvider);
    soundService.stop();
    Navigator.push(
      context,
      MaterialPageRoute(builder: (_) => const ImmersiveListeningView()),
    );
  }

}

/// 开发者测试包装器
/// 用于处理从开发者选项进入的测试界面的返回逻辑
class DeveloperTestWrapper extends StatelessWidget {
  final Widget child;
  final VoidCallback onBack;

  const DeveloperTestWrapper({
    super.key,
    required this.child,
    required this.onBack,
  });

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // 当用户点击系统返回按钮时，触发自定义返回逻辑
        Navigator.of(context).pop();
        onBack();
        return false; // 阻止默认的返回行为
      },
      child: child,
    );
  }
}
