import 'package:flutter/foundation.dart';

/// STT识别结果
@immutable
class SttResult {
  /// 识别出的文字内容
  final String text;
  
  /// 是否为最终结果（true=最终结果，false=中间结果）
  final bool isFinal;
  
  /// 置信度 (0.0-1.0)
  final double confidence;
  
  /// 识别时间戳
  final DateTime timestamp;
  
  /// 语音段ID（用于区分不同的语音段）
  final String? segmentId;

  /// 语言代码
  final String? language;

  /// 备选识别结果
  final List<String> alternatives;

  const SttResult({
    required this.text,
    required this.isFinal,
    required this.confidence,
    required this.timestamp,
    this.segmentId,
    this.language,
    this.alternatives = const [],
  });

  @override
  String toString() {
    return 'SttResult(text: "$text", isFinal: $isFinal, confidence: $confidence, segmentId: $segmentId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SttResult &&
        other.text == text &&
        other.isFinal == isFinal &&
        other.confidence == confidence &&
        other.timestamp == timestamp &&
        other.segmentId == segmentId;
  }

  @override
  int get hashCode {
    return text.hashCode ^
        isFinal.hashCode ^
        confidence.hashCode ^
        timestamp.hashCode ^
        segmentId.hashCode;
  }
}

/// STT错误信息
@immutable
class SttError {
  /// 错误代码
  final String code;
  
  /// 错误消息
  final String message;
  
  /// 错误类型
  final SttErrorType type;
  
  /// 错误时间戳
  final DateTime timestamp;
  
  /// 原始异常（如果有）
  final Object? originalError;

  const SttError({
    required this.code,
    required this.message,
    required this.type,
    required this.timestamp,
    this.originalError,
    Object? details, // 别名，指向 originalError
  }) : originalError = details ?? originalError;

  @override
  String toString() {
    return 'SttError(code: $code, message: "$message", type: $type)';
  }
}

/// STT错误类型
enum SttErrorType {
  /// 网络连接错误
  network,
  
  /// 认证错误
  authentication,
  
  /// 音频格式错误
  audioFormat,
  
  /// 服务器错误
  server,
  
  /// 配置错误
  configuration,

  /// 限流错误
  rateLimit,

  /// 未知错误
  unknown,
}

/// STT连接状态
enum SttConnectionState {
  /// 未连接
  disconnected,
  
  /// 连接中
  connecting,
  
  /// 已连接
  connected,
  
  /// 监听中（正在接收音频）
  listening,
  
  /// 错误状态
  error,
}

/// STT配置
@immutable
class SttConfig {
  /// 采样率 (Hz)
  final int sampleRate;
  
  /// 音频格式
  final SttAudioFormat audioFormat;
  
  /// 语言代码 (如: zh-cn, en-us)
  final String language;
  
  /// 是否启用中间结果
  final bool enableInterimResults;
  
  /// 是否启用标点符号
  final bool enablePunctuation;
  
  /// 静音检测超时时间 (毫秒)
  final int silenceTimeout;
  
  /// 最大录音时长 (毫秒)
  final int maxRecordingDuration;

  /// 置信度阈值 (0.0-1.0)
  final double? confidenceThreshold;

  const SttConfig({
    this.sampleRate = 16000,
    this.audioFormat = SttAudioFormat.pcm16,
    this.language = 'zh-cn',
    this.enableInterimResults = true,
    this.enablePunctuation = true,
    this.silenceTimeout = 5000,
    this.maxRecordingDuration = 60000,
    this.confidenceThreshold,
  });

  SttConfig copyWith({
    int? sampleRate,
    SttAudioFormat? audioFormat,
    String? language,
    bool? enableInterimResults,
    bool? enablePunctuation,
    int? silenceTimeout,
    int? maxRecordingDuration,
    double? confidenceThreshold,
  }) {
    return SttConfig(
      sampleRate: sampleRate ?? this.sampleRate,
      audioFormat: audioFormat ?? this.audioFormat,
      language: language ?? this.language,
      enableInterimResults: enableInterimResults ?? this.enableInterimResults,
      enablePunctuation: enablePunctuation ?? this.enablePunctuation,
      silenceTimeout: silenceTimeout ?? this.silenceTimeout,
      maxRecordingDuration: maxRecordingDuration ?? this.maxRecordingDuration,
      confidenceThreshold: confidenceThreshold ?? this.confidenceThreshold,
    );
  }
}

/// 音频格式
enum SttAudioFormat {
  /// PCM 16位
  pcm16,
  
  /// PCM 8位
  pcm8,
  
  /// MP3
  mp3,
  
  /// AAC
  aac,
}

/// STT统计信息
@immutable
class SttStats {
  /// 总识别时长 (毫秒)
  final int totalDuration;
  
  /// 识别的字符数
  final int totalCharacters;
  
  /// 识别准确率 (0.0-1.0)
  final double averageConfidence;
  
  /// 网络延迟 (毫秒)
  final int networkLatency;
  
  /// 错误次数
  final int errorCount;

  /// 额外信息（用于扩展统计数据）
  final Map<String, dynamic>? additionalInfo;

  const SttStats({
    required this.totalDuration,
    required this.totalCharacters,
    required this.averageConfidence,
    required this.networkLatency,
    required this.errorCount,
    this.additionalInfo,
  });

  @override
  String toString() {
    return 'SttStats(duration: ${totalDuration}ms, chars: $totalCharacters, confidence: ${(averageConfidence * 100).toStringAsFixed(1)}%, latency: ${networkLatency}ms, errors: $errorCount)';
  }
}
