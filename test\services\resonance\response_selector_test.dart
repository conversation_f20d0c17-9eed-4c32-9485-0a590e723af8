/// 智能回应选择器单元测试

import 'package:flutter_test/flutter_test.dart';
import 'package:echo_cave/services/resonance/response_selector.dart';
import 'package:echo_cave/services/resonance/resonance_models.dart';
import 'package:echo_cave/services/resonance/resonance_engine_service.dart';

void main() {
  group('ResponseSelector Tests', () {
    setUp(() {
      // 清除历史记录
      ResponseSelector.clearHistory();
    });

    group('Random Selection', () {
      test('should select from response pool randomly', () {
        final rule = ResonanceRule(
          id: 'test_rule',
          name: 'Test Rule',
          description: 'Test rule',
          condition: KeywordCondition(keywords: ['test']),
          action: ResonanceAction(
            type: ResonanceActionType.acknowledgment,
            responsePool: ['Response 1', 'Response 2', 'Response 3'],
          ),
        );

        final context = <ConversationContextItem>[];

        final result = ResponseSelector.selectResponse(
          rule: rule,
          context: context,
          strategy: ResponseSelectionStrategy.random,
        );

        expect(result.selectedResponse, isIn(['Response 1', 'Response 2', 'Response 3']));
        expect(result.strategy, ResponseSelectionStrategy.random);
        expect(result.confidence, 0.7);
      });

      test('should handle empty response pool', () {
        final rule = ResonanceRule(
          id: 'empty_rule',
          name: 'Empty Rule',
          description: 'Rule with empty response pool',
          condition: KeywordCondition(keywords: ['test']),
          action: ResonanceAction(
            type: ResonanceActionType.acknowledgment,
            responsePool: [],
          ),
        );

        final context = <ConversationContextItem>[];

        final result = ResponseSelector.selectResponse(
          rule: rule,
          context: context,
          strategy: ResponseSelectionStrategy.random,
        );

        expect(result.selectedResponse, '嗯');
        expect(result.confidence, 0.5);
        expect(result.metadata['fallback'], true);
      });
    });

    group('Context-Aware Selection', () {
      test('should select based on emotion intensity', () {
        final rule = ResonanceRule(
          id: 'emotion_rule',
          name: 'Emotion Rule',
          description: 'Rule for emotion-based selection',
          condition: EmotionCondition(tendency: EmotionTendency.negative),
          action: ResonanceAction(
            type: ResonanceActionType.empathyResponse,
            responsePool: ['嗯', '我理解你的感受', '这确实很让人难过'],
          ),
        );

        final context = [
          ConversationContextItem(
            text: '我今天很难过，感觉很痛苦',
            timestamp: DateTime.now(),
            isFinal: true,
            confidence: 0.9,
          ),
        ];

        final result = ResponseSelector.selectResponse(
          rule: rule,
          context: context,
          strategy: ResponseSelectionStrategy.contextAware,
        );

        expect(result.selectedResponse, isIn(rule.action.responsePool));
        expect(result.strategy, ResponseSelectionStrategy.contextAware);
        expect(result.confidence, greaterThanOrEqualTo(0.7));
        expect(result.metadata['emotionIntensity'], greaterThan(0.0));
      });

      test('should avoid repetition when possible', () {
        final rule = ResonanceRule(
          id: 'repetition_rule',
          name: 'Repetition Rule',
          description: 'Rule for testing repetition avoidance',
          condition: KeywordCondition(keywords: ['test']),
          action: ResonanceAction(
            type: ResonanceActionType.acknowledgment,
            responsePool: ['Response A', 'Response B', 'Response C'],
          ),
        );

        final context = [
          ConversationContextItem(
            text: 'test message',
            timestamp: DateTime.now(),
            isFinal: true,
            confidence: 0.9,
          ),
        ];

        // 第一次选择
        final result1 = ResponseSelector.selectResponse(
          rule: rule,
          context: context,
          strategy: ResponseSelectionStrategy.contextAware,
        );

        // 第二次选择应该避免重复
        final result2 = ResponseSelector.selectResponse(
          rule: rule,
          context: context,
          strategy: ResponseSelectionStrategy.contextAware,
        );

        // 如果有多个选项，第二次应该选择不同的回应
        if (rule.action.responsePool.length > 1) {
          expect(result2.selectedResponse, isNot(equals(result1.selectedResponse)));
        }
      });
    });

    group('Anti-Repetitive Selection', () {
      test('should avoid recently used responses', () {
        final rule = ResonanceRule(
          id: 'anti_rep_rule',
          name: 'Anti-Repetitive Rule',
          description: 'Rule for testing anti-repetitive selection',
          condition: KeywordCondition(keywords: ['test']),
          action: ResonanceAction(
            type: ResonanceActionType.acknowledgment,
            responsePool: ['A', 'B', 'C', 'D'],
          ),
        );

        final context = <ConversationContextItem>[];
        final usedResponses = <String>{};

        // 进行多次选择
        for (int i = 0; i < 4; i++) {
          final result = ResponseSelector.selectResponse(
            rule: rule,
            context: context,
            strategy: ResponseSelectionStrategy.antiRepetitive,
          );

          usedResponses.add(result.selectedResponse);
          expect(result.strategy, ResponseSelectionStrategy.antiRepetitive);
        }

        // 应该使用了所有可用的回应
        expect(usedResponses.length, 4);
        expect(usedResponses, containsAll(['A', 'B', 'C', 'D']));
      });
    });

    group('Emotion-Based Selection', () {
      test('should detect emotion keywords', () {
        final rule = ResonanceRule(
          id: 'emotion_based_rule',
          name: 'Emotion-Based Rule',
          description: 'Rule for emotion-based selection',
          condition: EmotionCondition(tendency: EmotionTendency.negative),
          action: ResonanceAction(
            type: ResonanceActionType.comfort,
            responsePool: ['我理解', '别难过', '会好起来的'],
          ),
        );

        final context = [
          ConversationContextItem(
            text: '我很难过',
            timestamp: DateTime.now().subtract(Duration(seconds: 1)),
            isFinal: true,
            confidence: 0.9,
          ),
          ConversationContextItem(
            text: '感觉很痛苦',
            timestamp: DateTime.now(),
            isFinal: true,
            confidence: 0.9,
          ),
        ];

        final result = ResponseSelector.selectResponse(
          rule: rule,
          context: context,
          strategy: ResponseSelectionStrategy.emotionBased,
        );

        expect(result.selectedResponse, isIn(rule.action.responsePool));
        expect(result.strategy, ResponseSelectionStrategy.emotionBased);
        expect(result.metadata['detectedEmotions'], isA<List<String>>());
        expect(result.metadata['analyzedText'], contains('难过'));
      });
    });

    group('History Management', () {
      test('should track response history', () {
        final rule = ResonanceRule(
          id: 'history_rule',
          name: 'History Rule',
          description: 'Rule for testing history',
          condition: KeywordCondition(keywords: ['test']),
          action: ResonanceAction(
            type: ResonanceActionType.acknowledgment,
            responsePool: ['Response 1'],
          ),
        );

        final context = <ConversationContextItem>[];

        // 初始状态
        var stats = ResponseSelector.getHistoryStats();
        expect(stats['historySize'], 0);

        // 选择一个回应
        ResponseSelector.selectResponse(
          rule: rule,
          context: context,
          strategy: ResponseSelectionStrategy.random,
        );

        // 检查历史记录
        stats = ResponseSelector.getHistoryStats();
        expect(stats['historySize'], 1);
        expect(stats['recentResponses'], contains('Response 1'));
      });

      test('should clear history correctly', () {
        final rule = ResonanceRule(
          id: 'clear_rule',
          name: 'Clear Rule',
          description: 'Rule for testing history clearing',
          condition: KeywordCondition(keywords: ['test']),
          action: ResonanceAction(
            type: ResonanceActionType.acknowledgment,
            responsePool: ['Test Response'],
          ),
        );

        final context = <ConversationContextItem>[];

        // 添加一些历史记录
        ResponseSelector.selectResponse(
          rule: rule,
          context: context,
          strategy: ResponseSelectionStrategy.random,
        );

        var stats = ResponseSelector.getHistoryStats();
        expect(stats['historySize'], 1);

        // 清除历史记录
        ResponseSelector.clearHistory();

        stats = ResponseSelector.getHistoryStats();
        expect(stats['historySize'], 0);
      });
    });

    group('Metadata Handling', () {
      test('should include additional metadata', () {
        final rule = ResonanceRule(
          id: 'metadata_rule',
          name: 'Metadata Rule',
          description: 'Rule for testing metadata',
          condition: KeywordCondition(keywords: ['test']),
          action: ResonanceAction(
            type: ResonanceActionType.acknowledgment,
            responsePool: ['Test Response'],
          ),
        );

        final context = <ConversationContextItem>[];
        final additionalMetadata = {
          'customField': 'customValue',
          'testNumber': 42,
        };

        final result = ResponseSelector.selectResponse(
          rule: rule,
          context: context,
          strategy: ResponseSelectionStrategy.contextAware,
          additionalMetadata: additionalMetadata,
        );

        expect(result.metadata['customField'], 'customValue');
        expect(result.metadata['testNumber'], 42);
      });
    });
  });
}
