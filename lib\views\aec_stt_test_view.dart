import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../services/parallel_audio_io_service.dart';
import '../services/stt/aec_stt_bridge.dart';
import '../services/stt/stt_models.dart';
import 'main_view.dart';

/// AEC-STT集成测试界面
/// 用于验证AEC处理后的音频流与STT服务的集成效果
class AecSttTestView extends HookConsumerWidget {
  const AecSttTestView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final aecService = useState<ParallelAudioIOService?>(null);
    final aecSttBridge = useState<AecSttBridge?>(null);
    final isInitialized = useState(false);
    final isRecognizing = useState(false);
    final recognitionResults = useState<List<SttResult>>([]);
    final processedResults = useState<List<SttResult>>([]);
    final completedSegments = useState<List<String>>([]);
    final errorMessages = useState<List<SttError>>([]);
    final bridgeStats = useState<Map<String, dynamic>>({});
    final healthStatus = useState<String>('unknown');
    final recoveryActions = useState<List<String>>([]);

    // 获取背景音服务
    final ambientService = ref.read(ambientSoundServiceProvider);

    // 进入页面时暂停背景音，离开时恢复
    useEffect(() {
      ambientService.pause();
      return () {
        ambientService.resume();
      };
    }, []);

    // 定时更新统计信息
    useEffect(() {
      Timer? timer;
      if (isRecognizing.value && aecSttBridge.value != null) {
        timer = Timer.periodic(const Duration(seconds: 1), (_) {
          bridgeStats.value = aecSttBridge.value!.stats;
        });
      }
      return () => timer?.cancel();
    }, [isRecognizing.value]);

    // 初始化服务
    useEffect(() {
      Future.microtask(() async {
        final aecSvc = ParallelAudioIOService();
        final aecInitialized = await aecSvc.initialize();
        
        if (aecInitialized) {
          aecService.value = aecSvc;
          
          final bridge = AecSttBridge(aecSvc);
          final bridgeInitialized = await bridge.initialize();
          
          if (bridgeInitialized) {
            aecSttBridge.value = bridge;
            isInitialized.value = true;

            // 监听原始识别结果
            bridge.onResult.listen((result) {
              recognitionResults.value = [...recognitionResults.value, result];
            });

            // 监听处理后的结果
            bridge.onProcessedResult.listen((result) {
              processedResults.value = [...processedResults.value, result];
            });

            // 监听完成的段落
            bridge.onSegmentComplete.listen((segment) {
              completedSegments.value = [...completedSegments.value, segment];
            });

            // 监听错误
            bridge.onError.listen((error) {
              errorMessages.value = [...errorMessages.value, error];
            });

            // 监听健康状态变化
            bridge.onHealthChanged.listen((health) {
              healthStatus.value = health.toString().split('.').last;
            });

            // 监听恢复动作
            bridge.onRecoveryAction.listen((action) {
              final actionText = '${DateTime.now().toString().substring(11, 19)} - ${action.type.toString().split('.').last}: ${action.reason}';
              recoveryActions.value = [...recoveryActions.value, actionText];

              // 限制恢复动作列表大小
              if (recoveryActions.value.length > 20) {
                recoveryActions.value = recoveryActions.value.sublist(recoveryActions.value.length - 20);
              }
            });
          }
        }
      });

      return () {
        aecSttBridge.value?.dispose();
        aecService.value?.dispose();
      };
    }, []);

    return Scaffold(
      backgroundColor: const Color(0xFF0F0F23),
      appBar: AppBar(
        title: const Text('AEC-STT集成测试'),
        backgroundColor: const Color(0xFF16213E),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // 服务状态卡片
            _buildServiceStatusCard(aecSttBridge.value, bridgeStats.value, healthStatus.value),
            
            const SizedBox(height: 16),
            
            // 控制按钮
            _buildControlButtons(
              aecSttBridge.value,
              isInitialized.value,
              isRecognizing,
              recognitionResults,
              processedResults,
              completedSegments,
              errorMessages,
              bridgeStats,
              recoveryActions,
            ),
            
            const SizedBox(height: 16),
            
            // 结果显示区域
            Expanded(
              child: _buildResultsDisplay(
                recognitionResults.value,
                processedResults.value,
                completedSegments.value,
                errorMessages.value,
                recoveryActions.value,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建服务状态卡片
  Widget _buildServiceStatusCard(AecSttBridge? bridge, Map<String, dynamic> stats, String healthStatus) {
    return Card(
      color: const Color(0xFF16213E),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '服务状态',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            if (bridge != null) ...[
              _buildStatusRow('初始化状态', bridge.isInitialized ? '已初始化' : '未初始化'),
              _buildStatusRow('识别状态', bridge.isActive ? '识别中' : '空闲'),
              _buildStatusRow('AEC服务', bridge.hasAecService ? '可用' : '不可用'),
              _buildStatusRow('STT服务', bridge.hasSttService ? '可用' : '不可用'),
              _buildStatusRow('STT连接', bridge.isSttConnected ? '已连接' : '未连接'),
              _buildStatusRow('服务健康', _getHealthStatusDisplay(healthStatus)),
              
              if (stats.isNotEmpty) ...[
                const SizedBox(height: 8),
                const Divider(color: Colors.white24),
                const SizedBox(height: 8),
                _buildStatusRow('会话时长', '${stats['sessionDuration']?.toStringAsFixed(1) ?? '0'} 秒'),
                _buildStatusRow('音频数据', '${(stats['totalAudioBytes'] ?? 0) ~/ 1024} KB'),
                _buildStatusRow('处理率', stats['audioProcessingRate'] ?? '0%'),
                _buildStatusRow('识别次数', '${stats['resultCount'] ?? 0}'),
                _buildStatusRow('错误次数', '${stats['errorCount'] ?? 0}'),
              ],
            ] else ...[
              const Text(
                '服务未初始化',
                style: TextStyle(color: Colors.orange),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建状态行
  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(color: Colors.white70),
          ),
          Text(
            value,
            style: const TextStyle(color: Colors.white),
          ),
        ],
      ),
    );
  }

  /// 获取健康状态显示
  String _getHealthStatusDisplay(String healthStatus) {
    switch (healthStatus) {
      case 'healthy':
        return '🟢 健康';
      case 'degraded':
        return '🟡 降级';
      case 'unhealthy':
        return '🔴 不健康';
      case 'retrying':
        return '🔄 重试中';
      case 'failed':
        return '❌ 失败';
      case 'monitoring':
        return '👁️ 监控中';
      default:
        return '❓ 未知';
    }
  }

  /// 构建控制按钮
  Widget _buildControlButtons(
    AecSttBridge? bridge,
    bool isInitialized,
    ValueNotifier<bool> isRecognizing,
    ValueNotifier<List<SttResult>> recognitionResults,
    ValueNotifier<List<SttResult>> processedResults,
    ValueNotifier<List<String>> completedSegments,
    ValueNotifier<List<SttError>> errorMessages,
    ValueNotifier<Map<String, dynamic>> bridgeStats,
    ValueNotifier<List<String>> recoveryActions,
  ) {
    return Row(
      children: [
        // 开始识别按钮
        Expanded(
          child: ElevatedButton.icon(
            onPressed: isInitialized && !isRecognizing.value && bridge != null
                ? () => _startRecognition(bridge, isRecognizing, bridgeStats)
                : null,
            icon: const Icon(Icons.mic),
            label: const Text('开始AEC-STT识别'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        
        const SizedBox(width: 12),
        
        // 停止识别按钮
        Expanded(
          child: ElevatedButton.icon(
            onPressed: isRecognizing.value && bridge != null
                ? () => _stopRecognition(bridge, isRecognizing, bridgeStats)
                : null,
            icon: const Icon(Icons.stop),
            label: const Text('停止'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        
        const SizedBox(width: 12),
        
        // 清除结果按钮
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _clearResults(recognitionResults, processedResults, completedSegments, errorMessages, bridgeStats, recoveryActions),
            icon: const Icon(Icons.clear),
            label: const Text('清除'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  /// 开始识别
  Future<void> _startRecognition(
    AecSttBridge bridge,
    ValueNotifier<bool> isRecognizing,
    ValueNotifier<Map<String, dynamic>> bridgeStats,
  ) async {
    try {
      await bridge.startRecognition();
      isRecognizing.value = true;
      bridgeStats.value = {}; // 重置统计
    } catch (e) {
      debugPrint('开始AEC-STT识别失败: $e');
    }
  }

  /// 停止识别
  Future<void> _stopRecognition(
    AecSttBridge bridge,
    ValueNotifier<bool> isRecognizing,
    ValueNotifier<Map<String, dynamic>> bridgeStats,
  ) async {
    try {
      await bridge.stopRecognition();
      isRecognizing.value = false;
      bridgeStats.value = bridge.stats; // 获取最终统计
    } catch (e) {
      debugPrint('停止AEC-STT识别失败: $e');
    }
  }

  /// 清除结果
  void _clearResults(
    ValueNotifier<List<SttResult>> recognitionResults,
    ValueNotifier<List<SttResult>> processedResults,
    ValueNotifier<List<String>> completedSegments,
    ValueNotifier<List<SttError>> errorMessages,
    ValueNotifier<Map<String, dynamic>> bridgeStats,
    ValueNotifier<List<String>> recoveryActions,
  ) {
    recognitionResults.value = [];
    processedResults.value = [];
    completedSegments.value = [];
    errorMessages.value = [];
    recoveryActions.value = [];
    bridgeStats.value = {};
  }

  /// 构建结果显示区域
  Widget _buildResultsDisplay(
    List<SttResult> rawResults,
    List<SttResult> processedResults,
    List<String> segments,
    List<SttError> errors,
    List<String> recoveryActions,
  ) {
    return DefaultTabController(
      length: 5,
      child: Column(
        children: [
          const TabBar(
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white54,
            indicatorColor: Colors.green,
            isScrollable: true,
            tabs: [
              Tab(text: '原始结果'),
              Tab(text: '处理后结果'),
              Tab(text: '完成段落'),
              Tab(text: '错误日志'),
              Tab(text: '恢复动作'),
            ],
          ),

          Expanded(
            child: TabBarView(
              children: [
                // 原始识别结果页
                _buildResultsList(rawResults, '原始STT结果'),

                // 处理后结果页
                _buildResultsList(processedResults, '精细化处理后结果'),

                // 完成段落页
                _buildSegmentsList(segments),

                // 错误日志页
                _buildErrorsList(errors),

                // 恢复动作页
                _buildRecoveryActionsList(recoveryActions),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建识别结果列表
  Widget _buildResultsList(List<SttResult> results, String title) {
    if (results.isEmpty) {
      return Center(
        child: Text(
          '暂无$title',
          style: const TextStyle(color: Colors.white54),
        ),
      );
    }

    return ListView.builder(
      itemCount: results.length,
      itemBuilder: (context, index) {
        final result = results[index];
        return Card(
          color: const Color(0xFF16213E),
          margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          child: ListTile(
            leading: Icon(
              result.isFinal ? Icons.check_circle : Icons.radio_button_unchecked,
              color: result.isFinal ? Colors.green : Colors.orange,
            ),
            title: Text(
              result.text,
              style: TextStyle(
                color: Colors.white,
                fontWeight: result.isFinal ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            subtitle: Text(
              '置信度: ${(result.confidence * 100).toStringAsFixed(1)}% | ${result.timestamp.toString().substring(11, 19)}',
              style: const TextStyle(color: Colors.white70, fontSize: 12),
            ),
            trailing: Text(
              result.isFinal ? '最终' : '中间',
              style: TextStyle(
                color: result.isFinal ? Colors.green : Colors.orange,
                fontSize: 12,
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建段落列表
  Widget _buildSegmentsList(List<String> segments) {
    if (segments.isEmpty) {
      return const Center(
        child: Text(
          '暂无完成的段落',
          style: TextStyle(color: Colors.white54),
        ),
      );
    }

    return ListView.builder(
      itemCount: segments.length,
      itemBuilder: (context, index) {
        final segment = segments[index];
        return Card(
          color: const Color(0xFF16213E),
          margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.green,
              child: Text(
                '${index + 1}',
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
            ),
            title: Text(
              segment,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
            subtitle: Text(
              '段落长度: ${segment.length} 字符',
              style: const TextStyle(color: Colors.white70, fontSize: 12),
            ),
            trailing: IconButton(
              icon: const Icon(Icons.copy, color: Colors.white70, size: 16),
              onPressed: () {
                // 复制段落到剪贴板
                // Clipboard.setData(ClipboardData(text: segment));
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('段落已复制到剪贴板'),
                    duration: Duration(seconds: 1),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  /// 构建恢复动作列表
  Widget _buildRecoveryActionsList(List<String> actions) {
    if (actions.isEmpty) {
      return const Center(
        child: Text(
          '暂无恢复动作',
          style: TextStyle(color: Colors.white54),
        ),
      );
    }

    return ListView.builder(
      itemCount: actions.length,
      itemBuilder: (context, index) {
        final action = actions[actions.length - 1 - index]; // 倒序显示，最新的在上面
        final parts = action.split(' - ');
        final time = parts.isNotEmpty ? parts[0] : '';
        final content = parts.length > 1 ? parts.sublist(1).join(' - ') : action;

        return Card(
          color: const Color(0xFF16213E),
          margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          child: ListTile(
            leading: const Icon(Icons.healing, color: Colors.blue, size: 20),
            title: Text(
              content,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
              ),
            ),
            subtitle: Text(
              time,
              style: const TextStyle(color: Colors.white70, fontSize: 12),
            ),
            trailing: Text(
              '${index + 1}',
              style: const TextStyle(color: Colors.white54, fontSize: 12),
            ),
          ),
        );
      },
    );
  }

  /// 构建错误列表
  Widget _buildErrorsList(List<SttError> errors) {
    if (errors.isEmpty) {
      return const Center(
        child: Text(
          '暂无错误',
          style: TextStyle(color: Colors.white54),
        ),
      );
    }

    return ListView.builder(
      itemCount: errors.length,
      itemBuilder: (context, index) {
        final error = errors[index];
        return Card(
          color: const Color(0xFF16213E),
          margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          child: ListTile(
            leading: const Icon(Icons.error, color: Colors.red),
            title: Text(
              error.message,
              style: const TextStyle(color: Colors.white),
            ),
            subtitle: Text(
              '类型: ${error.type.toString().split('.').last} | 代码: ${error.code} | ${error.timestamp.toString().substring(11, 19)}',
              style: const TextStyle(color: Colors.white70, fontSize: 12),
            ),
          ),
        );
      },
    );
  }
}
