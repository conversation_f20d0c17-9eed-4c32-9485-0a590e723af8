/// TTS时机控制器单元测试

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:echo_cave/services/resonance/tts_timing_controller.dart';
import 'package:echo_cave/services/tts_service.dart';

/// Mock TTS Service for testing
class MockTtsService implements TTSService {
  bool _isPlaying = false;
  String? _lastText;
  VoidCallback? _completionHandler;
  Function(String)? _errorHandler;

  @override
  bool get isPlaying => _isPlaying;

  String? get lastText => _lastText;

  @override
  Future<void> speak(String text) async {
    _lastText = text;
    _isPlaying = true;

    // 模拟播放延迟
    await Future.delayed(const Duration(milliseconds: 100));

    // 模拟播放完成
    _isPlaying = false;
    _completionHandler?.call();
  }

  @override
  Future<void> stop() async {
    _isPlaying = false;
  }

  @override
  void setCompletionHandler(VoidCallback handler) {
    _completionHandler = handler;
  }

  @override
  void setStartHandler(void Function() onStart) {
    // Mock implementation
  }

  @override
  void setErrorHandler(Function(String) handler) {
    _errorHandler = handler;
  }

  // Test helper methods
  void simulateError(String error) {
    _isPlaying = false;
    _errorHandler?.call(error);
  }

  void reset() {
    _isPlaying = false;
    _lastText = null;
    _completionHandler = null;
    _errorHandler = null;
  }

  // 实现TTSService的其他方法
  @override
  Future<void> initialize() async {}

  @override
  Future<void> setLanguage(String language) async {}

  @override
  Future<void> setSpeechRate(double rate) async {}

  @override
  Future<void> setVolume(double volume) async {}

  @override
  Future<void> setPitch(double pitch) async {}

  @override
  Future<List<String>> getLanguages() async => ['zh-CN', 'en-US'];

  @override
  Future<List<String>> getVoices() async => ['voice1', 'voice2'];

  @override
  Future<void> setVoice(String voice) async {}

  @override
  Future<bool> get isLanguageAvailable async => true;
}

void main() {
  group('TtsTimingController Tests', () {
    late TtsTimingController controller;
    late MockTtsService mockTtsService;

    setUp(() {
      mockTtsService = MockTtsService();
      controller = TtsTimingController(
        ttsService: mockTtsService,
        enableSmartTiming: false, // 禁用智能时机以简化测试
      );
    });

    tearDown(() {
      controller.dispose();
      mockTtsService.reset();
    });

    group('Basic Functionality', () {
      test('should start in idle state', () {
        expect(controller.state, TtsPlaybackState.idle);
        expect(controller.isPlaying, false);
        expect(controller.isWaiting, false);
      });

      test('should handle immediate playback request', () async {
        final request = TtsPlaybackRequest(
          id: 'test_1',
          text: 'Hello world',
          requestTime: DateTime.now(),
        );

        final success = await controller.requestPlayback(request);

        expect(success, true);
        expect(mockTtsService.lastText, 'Hello world');
        expect(controller.state, TtsPlaybackState.playing);
      });

      test('should complete playback successfully', () async {
        final request = TtsPlaybackRequest(
          id: 'test_2',
          text: 'Test message',
          requestTime: DateTime.now(),
        );

        await controller.requestPlayback(request);

        // 等待播放完成
        await Future.delayed(const Duration(milliseconds: 200));

        expect(controller.state, TtsPlaybackState.completed);
        
        // 等待状态重置
        await Future.delayed(const Duration(milliseconds: 600));
        
        expect(controller.state, TtsPlaybackState.idle);
      });
    });

    group('Request Management', () {
      test('should reject new requests while playing', () async {
        final request1 = TtsPlaybackRequest(
          id: 'test_1',
          text: 'First message',
          requestTime: DateTime.now(),
        );

        final request2 = TtsPlaybackRequest(
          id: 'test_2',
          text: 'Second message',
          requestTime: DateTime.now(),
        );

        // 第一个请求应该成功
        final success1 = await controller.requestPlayback(request1);
        expect(success1, true);

        // 第二个请求应该被拒绝（因为正在播放）
        final success2 = await controller.requestPlayback(request2);
        expect(success2, false);

        expect(mockTtsService.lastText, 'First message');
      });

      test('should override waiting request with new request', () async {
        // 启用智能时机以测试等待状态
        final smartController = TtsTimingController(
          ttsService: mockTtsService,
          enableSmartTiming: true,
        );

        final request1 = TtsPlaybackRequest(
          id: 'test_1',
          text: 'First message',
          requestTime: DateTime.now(),
        );

        final request2 = TtsPlaybackRequest(
          id: 'test_2',
          text: 'Second message',
          requestTime: DateTime.now(),
        );

        // 第一个请求进入等待状态
        await smartController.requestPlayback(request1);
        expect(smartController.isWaiting, true);

        // 第二个请求应该覆盖第一个
        await smartController.requestPlayback(request2);
        expect(smartController.currentRequest?.text, 'Second message');

        smartController.dispose();
      });
    });

    group('Error Handling', () {
      test('should handle TTS errors gracefully', () async {
        final request = TtsPlaybackRequest(
          id: 'test_error',
          text: 'Error test',
          requestTime: DateTime.now(),
        );

        await controller.requestPlayback(request);

        // 模拟TTS错误
        mockTtsService.simulateError('TTS Error');

        // 等待错误处理
        await Future.delayed(const Duration(milliseconds: 300));

        expect(controller.state, TtsPlaybackState.idle);
      });
    });

    group('Statistics', () {
      test('should track playback statistics', () async {
        var stats = controller.getStats();
        expect(stats['totalRequests'], 0);
        expect(stats['successfulPlays'], 0);

        // 成功播放一个请求
        final request1 = TtsPlaybackRequest(
          id: 'stats_test_1',
          text: 'Stats test 1',
          requestTime: DateTime.now(),
        );

        await controller.requestPlayback(request1);
        await Future.delayed(const Duration(milliseconds: 200));

        stats = controller.getStats();
        expect(stats['totalRequests'], 1);
        expect(stats['successfulPlays'], 1);

        // 模拟一个错误
        final request2 = TtsPlaybackRequest(
          id: 'stats_test_2',
          text: 'Stats test 2',
          requestTime: DateTime.now(),
        );

        await controller.requestPlayback(request2);
        mockTtsService.simulateError('Test error');
        await Future.delayed(const Duration(milliseconds: 300));

        stats = controller.getStats();
        expect(stats['totalRequests'], 2);
        expect(stats['successfulPlays'], 1);
        expect(stats['cancelledPlays'], 1);
      });
    });

    group('Configuration', () {
      test('should update configuration correctly', () {
        controller.updateConfig(
          silenceThreshold: const Duration(milliseconds: 1000),
          maxWaitTime: const Duration(seconds: 10),
          enableSmartTiming: true,
        );

        // 配置更新应该触发通知
        // 这里我们只验证方法调用不会抛出异常
        expect(true, true);
      });
    });

    group('Force Stop', () {
      test('should force stop current playback', () async {
        final request = TtsPlaybackRequest(
          id: 'force_stop_test',
          text: 'Force stop test',
          requestTime: DateTime.now(),
        );

        await controller.requestPlayback(request);
        expect(controller.state, TtsPlaybackState.playing);

        await controller.forceStop();

        // 等待停止处理
        await Future.delayed(const Duration(milliseconds: 300));

        expect(controller.state, TtsPlaybackState.idle);
      });
    });

    group('Smart Timing', () {
      test('should handle smart timing when disabled', () async {
        // 智能时机已禁用，应该立即播放
        final request = TtsPlaybackRequest(
          id: 'smart_timing_test',
          text: 'Smart timing test',
          requestTime: DateTime.now(),
        );

        final success = await controller.requestPlayback(request);

        expect(success, true);
        expect(controller.state, TtsPlaybackState.playing);
        expect(mockTtsService.lastText, 'Smart timing test');
      });
    });
  });
}
