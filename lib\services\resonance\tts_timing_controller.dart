/// TTS播放时机控制器
/// 负责智能控制AI回应的播放时机，确保不打断用户说话

import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import '../tts_service.dart';
import '../stt/audio_activity_detector.dart';
import '../parallel_audio_io_service.dart';

/// TTS播放状态
enum TtsPlaybackState {
  /// 空闲状态
  idle,
  
  /// 等待播放时机
  waitingForOpportunity,
  
  /// 正在播放
  playing,
  
  /// 播放被取消
  cancelled,
  
  /// 播放完成
  completed,
}

/// 播放时机检测结果
class PlaybackOpportunityResult {
  final bool canPlay;
  final String reason;
  final Duration waitTime;

  const PlaybackOpportunityResult({
    required this.canPlay,
    required this.reason,
    this.waitTime = Duration.zero,
  });

  factory PlaybackOpportunityResult.canPlay(String reason) {
    return PlaybackOpportunityResult(
      canPlay: true,
      reason: reason,
    );
  }

  factory PlaybackOpportunityResult.mustWait(String reason, Duration waitTime) {
    return PlaybackOpportunityResult(
      canPlay: false,
      reason: reason,
      waitTime: waitTime,
    );
  }
}

/// TTS播放请求
class TtsPlaybackRequest {
  final String id;
  final String text;
  final DateTime requestTime;
  final Duration maxWaitTime;
  final int priority;

  const TtsPlaybackRequest({
    required this.id,
    required this.text,
    required this.requestTime,
    this.maxWaitTime = const Duration(seconds: 5),
    this.priority = 1,
  });
}

/// TTS时机控制器
class TtsTimingController extends ChangeNotifier {
  static const Duration _defaultSilenceThreshold = Duration(milliseconds: 500);
  static const Duration _defaultMaxWaitTime = Duration(seconds: 5);
  static const Duration _checkInterval = Duration(milliseconds: 100);

  // 服务依赖
  final TTSService _ttsService;
  final ParallelAudioIOService? _audioService;
  final AudioActivityDetector? _activityDetector;

  // 状态管理
  TtsPlaybackState _state = TtsPlaybackState.idle;
  TtsPlaybackRequest? _currentRequest;
  Timer? _opportunityTimer;
  Timer? _timeoutTimer;
  StreamSubscription<Uint8List>? _audioSubscription;

  // 配置
  Duration _silenceThreshold;
  Duration _maxWaitTime;
  bool _enableSmartTiming;

  // 统计信息
  int _totalRequests = 0;
  int _successfulPlays = 0;
  int _cancelledPlays = 0;
  int _timeoutPlays = 0;

  /// 构造函数
  TtsTimingController({
    required TTSService ttsService,
    ParallelAudioIOService? audioService,
    AudioActivityDetector? activityDetector,
    Duration silenceThreshold = _defaultSilenceThreshold,
    Duration maxWaitTime = _defaultMaxWaitTime,
    bool enableSmartTiming = true,
  })  : _ttsService = ttsService,
        _audioService = audioService,
        _activityDetector = activityDetector,
        _silenceThreshold = silenceThreshold,
        _maxWaitTime = maxWaitTime,
        _enableSmartTiming = enableSmartTiming;

  // Getters
  TtsPlaybackState get state => _state;
  TtsPlaybackRequest? get currentRequest => _currentRequest;
  bool get isPlaying => _state == TtsPlaybackState.playing;
  bool get isWaiting => _state == TtsPlaybackState.waitingForOpportunity;

  /// 获取统计信息
  Map<String, dynamic> getStats() {
    return {
      'totalRequests': _totalRequests,
      'successfulPlays': _successfulPlays,
      'cancelledPlays': _cancelledPlays,
      'timeoutPlays': _timeoutPlays,
      'successRate': _totalRequests > 0 ? _successfulPlays / _totalRequests : 0.0,
    };
  }

  /// 请求播放TTS
  Future<bool> requestPlayback(TtsPlaybackRequest request) async {
    if (_state == TtsPlaybackState.playing) {
      debugPrint('TTS正在播放中，忽略新请求: ${request.text}');
      return false;
    }

    // 取消当前等待的请求
    if (_state == TtsPlaybackState.waitingForOpportunity) {
      await _cancelCurrentRequest('新请求覆盖');
    }

    _totalRequests++;
    _currentRequest = request;
    _setState(TtsPlaybackState.waitingForOpportunity);

    debugPrint('收到TTS播放请求: "${request.text}"');

    try {
      if (!_enableSmartTiming) {
        // 直接播放，不等待时机
        return await _playImmediately();
      }

      // 智能时机检测
      final opportunity = await _checkPlaybackOpportunity();
      if (opportunity.canPlay) {
        return await _playImmediately();
      } else {
        return await _waitForOpportunity(request);
      }
    } catch (e) {
      debugPrint('TTS播放请求处理失败: $e');
      await _cancelCurrentRequest('处理失败: $e');
      return false;
    }
  }

  /// 立即播放
  Future<bool> _playImmediately() async {
    if (_currentRequest == null) return false;

    try {
      _setState(TtsPlaybackState.playing);
      
      // 设置TTS完成回调
      _ttsService.setCompletionHandler(() {
        _onPlaybackCompleted();
      });

      _ttsService.setErrorHandler((error) {
        _onPlaybackError(error);
      });

      await _ttsService.speak(_currentRequest!.text);
      
      debugPrint('TTS开始播放: "${_currentRequest!.text}"');
      return true;
    } catch (e) {
      debugPrint('TTS播放失败: $e');
      await _cancelCurrentRequest('播放失败: $e');
      return false;
    }
  }

  /// 等待播放时机
  Future<bool> _waitForOpportunity(TtsPlaybackRequest request) async {
    debugPrint('等待播放时机...');

    // 设置超时定时器
    _timeoutTimer = Timer(request.maxWaitTime, () {
      _onWaitTimeout();
    });

    // 开始监听音频活动
    _startAudioMonitoring();

    // 定期检查播放时机
    _opportunityTimer = Timer.periodic(_checkInterval, (timer) {
      _checkAndPlayIfOpportunity();
    });

    return true;
  }

  /// 检查播放时机
  Future<PlaybackOpportunityResult> _checkPlaybackOpportunity() async {
    // 如果没有音频服务或活动检测器，直接允许播放
    if (_audioService == null || _activityDetector == null) {
      return PlaybackOpportunityResult.canPlay('无音频监控，直接播放');
    }

    // 检查音频服务状态
    if (!_audioService!.isInitialized) {
      return PlaybackOpportunityResult.canPlay('音频服务未初始化，直接播放');
    }

    // 检查是否有语音活动
    if (_activityDetector!.isActive) {
      return PlaybackOpportunityResult.mustWait(
        '检测到语音活动',
        _silenceThreshold,
      );
    }

    // 检查静音持续时间
    if (_activityDetector!.silenceDuration < _silenceThreshold.inSeconds) {
      final remainingWait = _silenceThreshold.inMilliseconds - 
          (_activityDetector!.silenceDuration * 1000).round();
      return PlaybackOpportunityResult.mustWait(
        '静音时间不足',
        Duration(milliseconds: remainingWait),
      );
    }

    return PlaybackOpportunityResult.canPlay('检测到足够的静音时间');
  }

  /// 开始音频监听
  void _startAudioMonitoring() {
    if (_audioService?.audioStream == null) return;

    _audioSubscription = _audioService!.audioStream!.listen(
      (audioData) {
        // 音频数据会自动传递给活动检测器
        // 这里我们只需要监听状态变化
      },
      onError: (error) {
        debugPrint('音频监听错误: $error');
      },
    );
  }

  /// 停止音频监听
  void _stopAudioMonitoring() {
    _audioSubscription?.cancel();
    _audioSubscription = null;
  }

  /// 定期检查并播放
  void _checkAndPlayIfOpportunity() async {
    if (_state != TtsPlaybackState.waitingForOpportunity) return;

    final opportunity = await _checkPlaybackOpportunity();
    if (opportunity.canPlay) {
      debugPrint('找到播放时机: ${opportunity.reason}');
      _cleanupTimers();
      await _playImmediately();
    }
  }

  /// 等待超时处理
  void _onWaitTimeout() {
    debugPrint('等待播放时机超时，强制播放');
    _timeoutPlays++;
    _cleanupTimers();
    _playImmediately();
  }

  /// 播放完成回调
  void _onPlaybackCompleted() {
    debugPrint('TTS播放完成');
    _successfulPlays++;
    _setState(TtsPlaybackState.completed);
    _currentRequest = null;
    _cleanupTimers();
    
    // 延迟一段时间后重置状态
    Timer(const Duration(milliseconds: 500), () {
      if (_state == TtsPlaybackState.completed) {
        _setState(TtsPlaybackState.idle);
      }
    });
  }

  /// 播放错误回调
  void _onPlaybackError(String error) {
    debugPrint('TTS播放错误: $error');
    _cancelledPlays++;
    _cancelCurrentRequest('播放错误: $error');
  }

  /// 取消当前请求
  Future<void> _cancelCurrentRequest(String reason) async {
    if (_currentRequest == null) return;

    debugPrint('取消TTS请求: $reason');
    _cancelledPlays++;
    _setState(TtsPlaybackState.cancelled);
    _currentRequest = null;
    _cleanupTimers();

    // 停止TTS播放
    try {
      await _ttsService.stop();
    } catch (e) {
      debugPrint('停止TTS失败: $e');
    }

    // 延迟重置状态
    Timer(const Duration(milliseconds: 200), () {
      if (_state == TtsPlaybackState.cancelled) {
        _setState(TtsPlaybackState.idle);
      }
    });
  }

  /// 清理定时器
  void _cleanupTimers() {
    _opportunityTimer?.cancel();
    _opportunityTimer = null;
    _timeoutTimer?.cancel();
    _timeoutTimer = null;
    _stopAudioMonitoring();
  }

  /// 设置状态
  void _setState(TtsPlaybackState newState) {
    if (_state != newState) {
      _state = newState;
      notifyListeners();
    }
  }

  /// 更新配置
  void updateConfig({
    Duration? silenceThreshold,
    Duration? maxWaitTime,
    bool? enableSmartTiming,
  }) {
    if (silenceThreshold != null) {
      _silenceThreshold = silenceThreshold;
    }
    if (maxWaitTime != null) {
      _maxWaitTime = maxWaitTime;
    }
    if (enableSmartTiming != null) {
      _enableSmartTiming = enableSmartTiming;
    }
    notifyListeners();
  }

  /// 强制停止当前播放
  Future<void> forceStop() async {
    await _cancelCurrentRequest('强制停止');
  }

  @override
  void dispose() {
    _cleanupTimers();
    super.dispose();
  }
}
