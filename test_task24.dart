/// Task 24 测试运行脚本
/// 
/// 运行此脚本来执行所有Task 24相关的测试

import 'dart:io';

void main() async {
  print('🚀 开始运行 Task 24 共鸣引擎测试套件\n');

  final testFiles = [
    'test/services/resonance/resonance_models_test.dart',
    'test/services/resonance/condition_evaluator_test.dart',
    'test/services/resonance/response_selector_test.dart',
    'test/services/resonance/tts_timing_controller_test.dart',
    'test/services/resonance/resonance_engine_service_test.dart',
    'test/services/resonance/ai_emotion_analyzer_test.dart',
  ];

  int totalTests = 0;
  int passedTests = 0;
  int failedTests = 0;
  List<String> failedFiles = [];

  for (final testFile in testFiles) {
    print('📋 运行测试: $testFile');
    
    final result = await Process.run(
      'flutter',
      ['test', testFile, '--reporter=compact'],
      workingDirectory: '.',
    );

    if (result.exitCode == 0) {
      print('✅ 通过\n');
      passedTests++;
    } else {
      print('❌ 失败');
      print('错误输出: ${result.stderr}');
      print('标准输出: ${result.stdout}\n');
      failedTests++;
      failedFiles.add(testFile);
    }
    totalTests++;
  }

  print('📊 测试结果总结:');
  print('总测试文件: $totalTests');
  print('通过: $passedTests');
  print('失败: $failedTests');
  
  if (failedFiles.isNotEmpty) {
    print('\n❌ 失败的测试文件:');
    for (final file in failedFiles) {
      print('  - $file');
    }
  }

  if (failedTests == 0) {
    print('\n🎉 所有测试都通过了！Task 24 共鸣引擎功能正常。');
    print('\n📱 接下来您可以运行演示应用来手动测试:');
    print('1. 在 main.dart 中添加演示页面路由');
    print('2. 运行 flutter run');
    print('3. 导航到共鸣引擎演示页面');
  } else {
    print('\n⚠️  有测试失败，请检查上述错误信息。');
    exit(1);
  }
}
