/// 条件评估器单元测试

import 'package:flutter_test/flutter_test.dart';
import 'package:echo_cave/services/resonance/condition_evaluator.dart';
import 'package:echo_cave/services/resonance/resonance_models.dart';
import 'package:echo_cave/services/resonance/resonance_engine_service.dart';

void main() {
  group('ConditionEvaluator Tests', () {
    group('Keyword Condition', () {
      test('should trigger on exact keyword match', () async {
        final condition = KeywordCondition(
          keywords: ['好累', '疲惫'],
          caseSensitive: false,
          exactMatch: false,
        );

        final context = [
          ConversationContextItem(
            text: '我今天好累啊',
            timestamp: DateTime.now(),
            isFinal: true,
            confidence: 0.9,
          ),
        ];

        final rule = ResonanceRule(
          id: 'test_rule',
          name: 'Test Rule',
          description: 'Test keyword rule',
          condition: condition,
          action: ResonanceAction(
            type: ResonanceActionType.empathyResponse,
            responsePool: ['我理解'],
          ),
        );

        final results = await ConditionEvaluator.evaluateRules([rule], context);

        expect(results.length, 1);
        expect(results.first.rule.id, 'test_rule');
        expect(results.first.evaluationResult.isTriggered, true);
        expect(results.first.evaluationResult.metadata['matchedKeyword'], '好累');
      });

      test('should not trigger when keyword not found', () async {
        final condition = KeywordCondition(
          keywords: ['开心', '快乐'],
          caseSensitive: false,
          exactMatch: false,
        );

        final context = [
          ConversationContextItem(
            text: '我今天好累啊',
            timestamp: DateTime.now(),
            isFinal: true,
            confidence: 0.9,
          ),
        ];

        final rule = ResonanceRule(
          id: 'test_rule',
          name: 'Test Rule',
          description: 'Test keyword rule',
          condition: condition,
          action: ResonanceAction(
            type: ResonanceActionType.empathyResponse,
            responsePool: ['我理解'],
          ),
        );

        final results = await ConditionEvaluator.evaluateRules([rule], context);

        expect(results.length, 0);
      });

      test('should respect case sensitivity', () async {
        final condition = KeywordCondition(
          keywords: ['Hello'],
          caseSensitive: true,
          exactMatch: false,
        );

        final context = [
          ConversationContextItem(
            text: 'hello world',
            timestamp: DateTime.now(),
            isFinal: true,
            confidence: 0.9,
          ),
        ];

        final rule = ResonanceRule(
          id: 'test_rule',
          name: 'Test Rule',
          description: 'Test case sensitive rule',
          condition: condition,
          action: ResonanceAction(
            type: ResonanceActionType.acknowledgment,
            responsePool: ['OK'],
          ),
        );

        final results = await ConditionEvaluator.evaluateRules([rule], context);

        expect(results.length, 0);
      });
    });

    group('Pause Condition', () {
      test('should trigger on sufficient pause duration', () async {
        final condition = PauseCondition(
          minDuration: Duration(milliseconds: 1000),
          maxDuration: Duration(milliseconds: 5000),
        );

        final now = DateTime.now();
        final context = [
          ConversationContextItem(
            text: '第一句话',
            timestamp: now.subtract(Duration(milliseconds: 2000)),
            isFinal: true,
            confidence: 0.9,
          ),
          ConversationContextItem(
            text: '第二句话',
            timestamp: now,
            isFinal: true,
            confidence: 0.9,
          ),
        ];

        final rule = ResonanceRule(
          id: 'pause_rule',
          name: 'Pause Rule',
          description: 'Test pause rule',
          condition: condition,
          action: ResonanceAction(
            type: ResonanceActionType.acknowledgment,
            responsePool: ['嗯'],
          ),
        );

        final results = await ConditionEvaluator.evaluateRules([rule], context);

        expect(results.length, 1);
        expect(results.first.rule.id, 'pause_rule');
        expect(results.first.evaluationResult.isTriggered, true);
        expect(results.first.evaluationResult.metadata['pauseDuration'], 2000);
      });

      test('should not trigger on insufficient pause duration', () async {
        final condition = PauseCondition(
          minDuration: Duration(milliseconds: 2000),
          maxDuration: Duration(milliseconds: 5000),
        );

        final now = DateTime.now();
        final context = [
          ConversationContextItem(
            text: '第一句话',
            timestamp: now.subtract(Duration(milliseconds: 500)),
            isFinal: true,
            confidence: 0.9,
          ),
          ConversationContextItem(
            text: '第二句话',
            timestamp: now,
            isFinal: true,
            confidence: 0.9,
          ),
        ];

        final rule = ResonanceRule(
          id: 'pause_rule',
          name: 'Pause Rule',
          description: 'Test pause rule',
          condition: condition,
          action: ResonanceAction(
            type: ResonanceActionType.acknowledgment,
            responsePool: ['嗯'],
          ),
        );

        final results = await ConditionEvaluator.evaluateRules([rule], context);

        expect(results.length, 0);
      });

      test('should not trigger with insufficient context', () async {
        final condition = PauseCondition(
          minDuration: Duration(milliseconds: 1000),
        );

        final context = [
          ConversationContextItem(
            text: '只有一句话',
            timestamp: DateTime.now(),
            isFinal: true,
            confidence: 0.9,
          ),
        ];

        final rule = ResonanceRule(
          id: 'pause_rule',
          name: 'Pause Rule',
          description: 'Test pause rule',
          condition: condition,
          action: ResonanceAction(
            type: ResonanceActionType.acknowledgment,
            responsePool: ['嗯'],
          ),
        );

        final results = await ConditionEvaluator.evaluateRules([rule], context);

        expect(results.length, 0);
      });
    });

    group('Emotion Condition', () {
      test('should trigger on negative emotion keywords', () async {
        final condition = EmotionCondition(
          tendency: EmotionTendency.negative,
          threshold: 0.5,
        );

        final context = [
          ConversationContextItem(
            text: '我今天很难过，感觉很痛苦',
            timestamp: DateTime.now(),
            isFinal: true,
            confidence: 0.9,
          ),
        ];

        final rule = ResonanceRule(
          id: 'emotion_rule',
          name: 'Emotion Rule',
          description: 'Test emotion rule',
          condition: condition,
          action: ResonanceAction(
            type: ResonanceActionType.comfort,
            responsePool: ['我理解你的感受'],
          ),
        );

        final results = await ConditionEvaluator.evaluateRules([rule], context);

        expect(results.length, 1);
        expect(results.first.rule.id, 'emotion_rule');
        expect(results.first.evaluationResult.isTriggered, true);
        expect(results.first.evaluationResult.metadata['emotionTendency'], 'negative');
      });

      test('should not trigger on positive text for negative emotion condition', () async {
        final condition = EmotionCondition(
          tendency: EmotionTendency.negative,
          threshold: 0.5,
        );

        final context = [
          ConversationContextItem(
            text: '我今天很开心，感觉很棒',
            timestamp: DateTime.now(),
            isFinal: true,
            confidence: 0.9,
          ),
        ];

        final rule = ResonanceRule(
          id: 'emotion_rule',
          name: 'Emotion Rule',
          description: 'Test emotion rule',
          condition: condition,
          action: ResonanceAction(
            type: ResonanceActionType.comfort,
            responsePool: ['我理解你的感受'],
          ),
        );

        final results = await ConditionEvaluator.evaluateRules([rule], context);

        expect(results.length, 0);
      });
    });

    group('Speech Rate Condition', () {
      test('should trigger on slow speech rate', () async {
        final condition = SpeechRateCondition(
          minWordsPerSecond: 0.0,
          maxWordsPerSecond: 2.0,
        );

        final now = DateTime.now();
        final context = [
          ConversationContextItem(
            text: '我',
            timestamp: now.subtract(Duration(milliseconds: 2000)),
            isFinal: true,
            confidence: 0.9,
          ),
          ConversationContextItem(
            text: '很',
            timestamp: now,
            isFinal: true,
            confidence: 0.9,
          ),
        ];

        final rule = ResonanceRule(
          id: 'speech_rate_rule',
          name: 'Speech Rate Rule',
          description: 'Test speech rate rule',
          condition: condition,
          action: ResonanceAction(
            type: ResonanceActionType.acknowledgment,
            responsePool: ['慢慢说'],
          ),
        );

        final results = await ConditionEvaluator.evaluateRules([rule], context);

        expect(results.length, 1);
        expect(results.first.rule.id, 'speech_rate_rule');
        expect(results.first.evaluationResult.isTriggered, true);
      });
    });

    group('Rule Priority', () {
      test('should return rules sorted by priority', () async {
        final lowPriorityRule = ResonanceRule(
          id: 'low_priority',
          name: 'Low Priority Rule',
          description: 'Low priority test rule',
          condition: KeywordCondition(keywords: ['测试']),
          action: ResonanceAction(
            type: ResonanceActionType.acknowledgment,
            responsePool: ['低优先级'],
          ),
          priority: 1,
        );

        final highPriorityRule = ResonanceRule(
          id: 'high_priority',
          name: 'High Priority Rule',
          description: 'High priority test rule',
          condition: KeywordCondition(keywords: ['测试']),
          action: ResonanceAction(
            type: ResonanceActionType.acknowledgment,
            responsePool: ['高优先级'],
          ),
          priority: 5,
        );

        final context = [
          ConversationContextItem(
            text: '这是一个测试',
            timestamp: DateTime.now(),
            isFinal: true,
            confidence: 0.9,
          ),
        ];

        final results = await ConditionEvaluator.evaluateRules(
          [lowPriorityRule, highPriorityRule],
          context,
        );

        expect(results.length, 2);
        expect(results.first.rule.id, 'high_priority');
        expect(results.last.rule.id, 'low_priority');
      });
    });
  });
}
