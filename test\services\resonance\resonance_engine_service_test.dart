/// ResonanceEngineService 单元测试

import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:echo_cave/services/resonance/resonance_engine_service.dart';
import 'package:echo_cave/services/resonance/resonance_models.dart';
import 'package:echo_cave/services/stt/streaming_stt_service.dart';
import 'package:echo_cave/services/stt/stt_models.dart';

/// Mock STT Service for testing
class MockStreamingSttService extends StreamingSttService {
  final StreamController<SttResult> _resultController = StreamController<SttResult>.broadcast();
  final StreamController<SttError> _errorController = StreamController<SttError>.broadcast();
  final StreamController<SttConnectionState> _stateController = StreamController<SttConnectionState>.broadcast();
  final StreamController<void> _endOfSpeechController = StreamController<void>.broadcast();

  @override
  SttConnectionState get connectionState => SttConnectionState.connected;

  @override
  SttConfig get config => SttConfig(
    language: 'zh-cn',
  );

  @override
  SttStats get stats => SttStats(
    totalDuration: 0,
    totalCharacters: 0,
    averageConfidence: 0.9,
    networkLatency: 100,
    errorCount: 0,
  );

  @override
  bool get isListening => true;

  @override
  Stream<SttResult> get onResult => _resultController.stream;

  @override
  Stream<SttError> get onError => _errorController.stream;

  @override
  Stream<SttConnectionState> get onConnectionStateChanged => _stateController.stream;

  @override
  Stream<void> get onEndOfSpeech => _endOfSpeechController.stream;

  @override
  Future<bool> initialize(SttConfig config) async => true;

  @override
  Future<void> startListening() async {}

  @override
  Future<void> stopListening() async {}

  @override
  Future<void> sendAudioData(List<int> audioData) async {}

  @override
  Future<void> disconnect() async {}

  @override
  Future<void> reset() async {}

  @override
  Future<List<String>> getSupportedLanguages() async => ['zh-cn', 'en-us'];

  @override
  Future<int> testConnection() async => 200;

  @override
  void dispose() {
    _resultController.close();
    _errorController.close();
    _stateController.close();
    _endOfSpeechController.close();
    super.dispose();
  }

  // Test helper methods
  void simulateSttResult(String text, {bool isFinal = true, DateTime? timestamp}) {
    final result = SttResult(
      text: text,
      isFinal: isFinal,
      confidence: 0.9,
      timestamp: timestamp ?? DateTime.now(),
      alternatives: [],
    );
    _resultController.add(result);
  }

  void simulateError(String message) {
    final error = SttError(
      code: 'TEST_ERROR',
      message: message,
      type: SttErrorType.unknown,
      timestamp: DateTime.now(),
    );
    _errorController.add(error);
  }
}

void main() {
  group('ResonanceEngineService Tests', () {
    late ResonanceEngineService service;
    late MockStreamingSttService mockSttService;

    setUp(() {
      service = ResonanceEngineService(
        contextDuration: const Duration(seconds: 10),
        cooldownDuration: const Duration(milliseconds: 100), // 短全局冷却期用于测试
      );
      mockSttService = MockStreamingSttService();
    });

    tearDown(() {
      service.dispose();
      mockSttService.dispose();
    });

    group('Initialization', () {
      test('should start in uninitialized state', () {
        expect(service.state, ResonanceEngineState.uninitialized);
        expect(service.ruleSet, isNull);
        expect(service.activeRules, isEmpty);
      });

      test('should initialize successfully with test rules', () async {
        // Create a test rule set
        final testRuleSet = ResonanceRuleSet(
          version: '1.0.0',
          name: 'Test Rules',
          description: 'Test rule set',
          rules: [
            ResonanceRule(
              id: 'test_rule',
              name: 'Test Rule',
              description: 'A test rule',
              condition: KeywordCondition(keywords: ['test']),
              action: ResonanceAction(
                type: ResonanceActionType.acknowledgment,
                responsePool: ['I understand'],
              ),
            ),
          ],
        );

        await service.loadRules(testRuleSet);

        expect(service.state, ResonanceEngineState.uninitialized);
        expect(service.ruleSet, isNotNull);
        expect(service.activeRules.length, 1);
        expect(service.activeRules.first.id, 'test_rule');
      });
    });

    group('STT Integration', () {
      setUp(() async {
        final testRuleSet = ResonanceRuleSet(
          version: '1.0.0',
          name: 'Test Rules',
          description: 'Test rule set',
          rules: [
            ResonanceRule(
              id: 'keyword_rule',
              name: 'Keyword Rule',
              description: 'A keyword test rule',
              condition: KeywordCondition(keywords: ['hello']),
              action: ResonanceAction(
                type: ResonanceActionType.acknowledgment,
                responsePool: ['Hi there'],
              ),
            ),
          ],
        );

        await service.loadRules(testRuleSet);
        service.setStateForTesting(ResonanceEngineState.initialized);
      });

      test('should start and subscribe to STT service', () async {
        await service.start(mockSttService);

        expect(service.state, ResonanceEngineState.running);
      });

      test('should receive and process STT results', () async {
        await service.start(mockSttService);

        // Listen for context changes
        bool contextChanged = false;
        service.addListener(() {
          contextChanged = true;
        });

        // Simulate STT result
        mockSttService.simulateSttResult('Hello world');

        // Wait for processing
        await Future.delayed(const Duration(milliseconds: 100));

        expect(contextChanged, true);
        expect(service.context.length, 1);
        expect(service.context.first.text, 'Hello world');
      });

      test('should ignore non-final STT results', () async {
        await service.start(mockSttService);

        // Simulate non-final result
        mockSttService.simulateSttResult('Hello', isFinal: false);

        // Wait for processing
        await Future.delayed(const Duration(milliseconds: 100));

        expect(service.context.length, 0);
      });

      test('should stop and clean up properly', () async {
        await service.start(mockSttService);
        expect(service.state, ResonanceEngineState.running);

        await service.stop();

        expect(service.state, ResonanceEngineState.stopped);
        expect(service.context, isEmpty);
      });
    });

    group('Context Management', () {
      test('should add context items manually', () {
        service.addContext('Test message');

        expect(service.context.length, 1);
        expect(service.context.first.text, 'Test message');
        expect(service.context.first.isFinal, true);
      });

      test('should clean up old context items', () async {
        final oldTimestamp = DateTime.now().subtract(const Duration(seconds: 15));
        final recentTimestamp = DateTime.now();

        service.addContext('Old message', timestamp: oldTimestamp);
        service.addContext('Recent message', timestamp: recentTimestamp);

        expect(service.context.length, 1);
        expect(service.context.first.text, 'Recent message');
      });

      test('should clear context when requested', () {
        service.addContext('Test message 1');
        service.addContext('Test message 2');

        expect(service.context.length, 2);

        service.clearContext();

        expect(service.context.length, 0);
      });
    });

    group('Cooldown Mechanism', () {
      test('should not be in cooldown initially', () {
        expect(service.isInCooldown, false);
      });

      test('should reset cooldown when requested', () {
        // We can't directly access private fields, so we'll test the public interface
        service.resetCooldown();
        expect(service.isInCooldown, false);
      });
    });

    group('Response Events', () {
      test('should provide response event stream', () {
        expect(service.onResponse, isA<Stream<ResonanceResponseEvent>>());
      });
    });

    group('Rule Evaluation Integration', () {
      setUp(() async {
        final testRuleSet = ResonanceRuleSet(
          version: '1.0.0',
          name: 'Integration Test Rules',
          description: 'Rules for integration testing',
          rules: [
            ResonanceRule(
              id: 'keyword_tired',
              name: 'Tired Keyword Rule',
              description: 'Detects tired keywords',
              condition: KeywordCondition(keywords: ['累', '疲惫']),
              action: ResonanceAction(
                type: ResonanceActionType.empathyResponse,
                responsePool: ['我理解你的疲惫'],
              ),
              priority: 2,
              cooldown: Duration(seconds: 1), // Short cooldown for testing
            ),
            ResonanceRule(
              id: 'pause_detection',
              name: 'Pause Detection Rule',
              description: 'Detects pauses in speech',
              condition: PauseCondition(
                minDuration: Duration(milliseconds: 500),
                maxDuration: Duration(milliseconds: 3000),
              ),
              action: ResonanceAction(
                type: ResonanceActionType.acknowledgment,
                responsePool: ['嗯', '我在听'],
              ),
              priority: 1,
            ),
          ],
        );

        await service.loadRules(testRuleSet);
        service.setStateForTesting(ResonanceEngineState.initialized);
      });

      test('should trigger keyword rule and emit response event', () async {
        await service.start(mockSttService);

        // Listen for response events
        ResonanceResponseEvent? responseEvent;
        final subscription = service.onResponse.listen((event) {
          responseEvent = event;
        });

        // Simulate STT result with keyword
        mockSttService.simulateSttResult('我今天好累啊');

        // Wait for processing
        await Future.delayed(const Duration(milliseconds: 200));

        expect(responseEvent, isNotNull);
        expect(responseEvent!.ruleId, 'keyword_tired');
        expect(responseEvent!.actionType, ResonanceActionType.empathyResponse);
        expect(responseEvent!.responseText, '我理解你的疲惫');

        await subscription.cancel();
      });

      test('should trigger pause rule with sufficient delay', () async {
        await service.start(mockSttService);

        // Listen for response events
        ResonanceResponseEvent? responseEvent;
        final subscription = service.onResponse.listen((event) {
          responseEvent = event;
        });

        // Simulate first STT result
        mockSttService.simulateSttResult('第一句话');
        await Future.delayed(const Duration(milliseconds: 100));

        // Simulate second STT result after a pause
        mockSttService.simulateSttResult(
          '第二句话',
          timestamp: DateTime.now().add(Duration(milliseconds: 1000)),
        );

        // Wait for processing
        await Future.delayed(const Duration(milliseconds: 200));

        expect(responseEvent, isNotNull);
        expect(responseEvent!.ruleId, 'pause_detection');
        expect(responseEvent!.actionType, ResonanceActionType.acknowledgment);
        expect(['嗯', '我在听'].contains(responseEvent!.responseText), true);

        await subscription.cancel();
      });

      test('should respect rule priority', () async {
        await service.start(mockSttService);

        // Listen for response events
        final responseEvents = <ResonanceResponseEvent>[];
        final subscription = service.onResponse.listen((event) {
          responseEvents.add(event);
        });

        // Simulate STT result that triggers both rules
        // First result
        mockSttService.simulateSttResult('我很累');
        await Future.delayed(const Duration(milliseconds: 100));

        // Second result after a pause (should trigger both keyword and pause rules)
        mockSttService.simulateSttResult(
          '真的很疲惫',
          timestamp: DateTime.now().add(Duration(milliseconds: 1000)),
        );

        // Wait for processing
        await Future.delayed(const Duration(milliseconds: 200));

        // Should only get one response (highest priority)
        expect(responseEvents.length, 1);
        expect(responseEvents.first.ruleId, 'keyword_tired'); // Higher priority

        await subscription.cancel();
      });

      test('should respect cooldown period', () async {
        await service.start(mockSttService);

        // Listen for response events
        final responseEvents = <ResonanceResponseEvent>[];
        final subscription = service.onResponse.listen((event) {
          responseEvents.add(event);
        });

        // First trigger
        mockSttService.simulateSttResult('我很累');
        await Future.delayed(const Duration(milliseconds: 200));

        expect(responseEvents.length, 1);

        // Second trigger immediately (should be blocked by cooldown)
        mockSttService.simulateSttResult('我很疲惫');
        await Future.delayed(const Duration(milliseconds: 200));

        expect(responseEvents.length, 1); // Still only one response

        // Wait for cooldown to expire (1 second + buffer)
        await Future.delayed(const Duration(milliseconds: 1200));

        // Manually reset cooldown for testing
        service.resetCooldown();

        // Third trigger (should work after cooldown)
        mockSttService.simulateSttResult('我真的很累');
        await Future.delayed(const Duration(milliseconds: 200));

        expect(responseEvents.length, 2); // Now we should have two responses

        await subscription.cancel();
      });
    });

    group('Advanced Response Selection', () {
      setUp(() async {
        final testRuleSet = ResonanceRuleSet(
          version: '1.0.0',
          name: 'Advanced Response Test Rules',
          description: 'Rules for testing advanced response selection',
          rules: [
            ResonanceRule(
              id: 'multi_response_rule',
              name: 'Multi Response Rule',
              description: 'Rule with multiple response options',
              condition: KeywordCondition(keywords: ['测试']),
              action: ResonanceAction(
                type: ResonanceActionType.acknowledgment,
                responsePool: ['短回应', '这是一个中等长度的回应', '这是一个相对较长的回应，包含更多的同理心和理解'],
              ),
              priority: 1,
              cooldown: Duration(milliseconds: 500),
            ),
          ],
        );

        await service.loadRules(testRuleSet);
        service.setStateForTesting(ResonanceEngineState.initialized);
      });

      test('should use intelligent response selection', () async {
        await service.start(mockSttService);

        // Listen for response events
        ResonanceResponseEvent? responseEvent;
        final subscription = service.onResponse.listen((event) {
          responseEvent = event;
        });

        // Simulate STT result
        mockSttService.simulateSttResult('这是一个测试');

        // Wait for processing
        await Future.delayed(const Duration(milliseconds: 200));

        expect(responseEvent, isNotNull);
        expect(responseEvent!.ruleId, 'multi_response_rule');
        expect(responseEvent!.metadata['selectionStrategy'], isNotNull);
        expect(responseEvent!.metadata['selectionConfidence'], isA<double>());

        await subscription.cancel();
      });
    });

    group('Rule-Specific Cooldowns', () {
      setUp(() async {
        final testRuleSet = ResonanceRuleSet(
          version: '1.0.0',
          name: 'Cooldown Test Rules',
          description: 'Rules for testing rule-specific cooldowns',
          rules: [
            ResonanceRule(
              id: 'short_cooldown_rule',
              name: 'Short Cooldown Rule',
              description: 'Rule with short cooldown',
              condition: KeywordCondition(keywords: ['快速']),
              action: ResonanceAction(
                type: ResonanceActionType.acknowledgment,
                responsePool: ['快速回应'],
              ),
              priority: 2,
              cooldown: Duration(milliseconds: 200),
            ),
            ResonanceRule(
              id: 'long_cooldown_rule',
              name: 'Long Cooldown Rule',
              description: 'Rule with long cooldown',
              condition: KeywordCondition(keywords: ['慢速']),
              action: ResonanceAction(
                type: ResonanceActionType.acknowledgment,
                responsePool: ['慢速回应'],
              ),
              priority: 1,
              cooldown: Duration(milliseconds: 1000),
            ),
          ],
        );

        await service.loadRules(testRuleSet);
        service.setStateForTesting(ResonanceEngineState.initialized);
      });

      test('should respect rule-specific cooldowns', () async {
        await service.start(mockSttService);

        final responseEvents = <ResonanceResponseEvent>[];
        final subscription = service.onResponse.listen((event) {
          responseEvents.add(event);
        });

        // Trigger short cooldown rule
        mockSttService.simulateSttResult('快速测试');
        await Future.delayed(const Duration(milliseconds: 100));

        expect(responseEvents.length, 1);
        expect(responseEvents.first.ruleId, 'short_cooldown_rule');

        // Try to trigger the same rule immediately (should be blocked)
        mockSttService.simulateSttResult('快速测试2');
        await Future.delayed(const Duration(milliseconds: 100));

        expect(responseEvents.length, 1); // Still only one response

        // Wait for rule-specific cooldown to expire
        await Future.delayed(const Duration(milliseconds: 200));

        // Try again (should work now)
        mockSttService.simulateSttResult('快速测试3');
        await Future.delayed(const Duration(milliseconds: 100));

        expect(responseEvents.length, 2); // Now we should have two responses

        await subscription.cancel();
      });

      test('should provide cooldown status information', () async {
        await service.start(mockSttService);

        // Trigger a rule
        mockSttService.simulateSttResult('快速测试');
        await Future.delayed(const Duration(milliseconds: 100));

        // Check cooldown status
        final cooldownStatus = service.getCooldownStatus();

        expect(cooldownStatus['isInGlobalCooldown'], true);
        expect(cooldownStatus['globalCooldownRemaining'], greaterThan(0));
        expect(cooldownStatus['ruleCooldowns'], isA<Map<String, int>>());
        expect(cooldownStatus['totalActiveRules'], 2);
      });

      test('should reset cooldowns correctly', () async {
        await service.start(mockSttService);

        // Trigger a rule
        mockSttService.simulateSttResult('快速测试');
        await Future.delayed(const Duration(milliseconds: 100));

        expect(service.isInCooldown, true);

        // Reset all cooldowns
        service.resetAllCooldowns();

        expect(service.isInCooldown, false);
        final cooldownStatus = service.getCooldownStatus();
        expect(cooldownStatus['isInGlobalCooldown'], false);
        expect(cooldownStatus['ruleCooldowns'], isEmpty);
      });
    });
  });
}
