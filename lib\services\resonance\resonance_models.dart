/// 共鸣引擎数据模型
/// 定义规则、条件、动作等核心数据结构

/// 规则条件类型枚举
enum ResonanceConditionType {
  /// 关键词匹配
  keyword,
  
  /// 停顿检测
  pause,
  
  /// 情绪分析
  emotion,
  
  /// 语速变化
  speechRate,
}

/// 规则动作类型枚举
enum ResonanceActionType {
  /// 共鸣回应
  empathyResponse,
  
  /// 鼓励回应
  encouragement,
  
  /// 确认回应
  acknowledgment,
  
  /// 安慰回应
  comfort,
}

/// 情绪倾向枚举
enum EmotionTendency {
  /// 积极
  positive,
  
  /// 消极
  negative,
  
  /// 中性
  neutral,
}

/// 规则条件基类
abstract class ResonanceCondition {
  final ResonanceConditionType type;
  final Map<String, dynamic> parameters;

  const ResonanceCondition({
    required this.type,
    required this.parameters,
  });

  /// 从JSON创建条件对象
  factory ResonanceCondition.fromJson(Map<String, dynamic> json) {
    final type = ResonanceConditionType.values.firstWhere(
      (e) => e.name == json['type'],
    );
    
    switch (type) {
      case ResonanceConditionType.keyword:
        return KeywordCondition.fromJson(json);
      case ResonanceConditionType.pause:
        return PauseCondition.fromJson(json);
      case ResonanceConditionType.emotion:
        return EmotionCondition.fromJson(json);
      case ResonanceConditionType.speechRate:
        return SpeechRateCondition.fromJson(json);
    }
  }

  /// 转换为JSON
  Map<String, dynamic> toJson();
}

/// 关键词条件
class KeywordCondition extends ResonanceCondition {
  final List<String> keywords;
  final bool caseSensitive;
  final bool exactMatch;

  KeywordCondition({
    required this.keywords,
    this.caseSensitive = false,
    this.exactMatch = false,
  }) : super(
          type: ResonanceConditionType.keyword,
          parameters: {
            'keywords': keywords,
            'caseSensitive': caseSensitive,
            'exactMatch': exactMatch,
          },
        );

  factory KeywordCondition.fromJson(Map<String, dynamic> json) {
    return KeywordCondition(
      keywords: List<String>.from(json['parameters']['keywords']),
      caseSensitive: json['parameters']['caseSensitive'] ?? false,
      exactMatch: json['parameters']['exactMatch'] ?? false,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'parameters': parameters,
    };
  }
}

/// 停顿条件
class PauseCondition extends ResonanceCondition {
  final Duration minDuration;
  final Duration maxDuration;

  PauseCondition({
    required this.minDuration,
    this.maxDuration = const Duration(seconds: 10),
  }) : super(
          type: ResonanceConditionType.pause,
          parameters: {
            'minDurationMs': minDuration.inMilliseconds,
            'maxDurationMs': maxDuration.inMilliseconds,
          },
        );

  factory PauseCondition.fromJson(Map<String, dynamic> json) {
    return PauseCondition(
      minDuration: Duration(milliseconds: json['parameters']['minDurationMs']),
      maxDuration: Duration(milliseconds: json['parameters']['maxDurationMs'] ?? 10000),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'parameters': parameters,
    };
  }
}

/// 情绪条件
class EmotionCondition extends ResonanceCondition {
  final EmotionTendency tendency;
  final double threshold;

  EmotionCondition({
    required this.tendency,
    this.threshold = 0.6,
  }) : super(
          type: ResonanceConditionType.emotion,
          parameters: {
            'tendency': tendency.name,
            'threshold': threshold,
          },
        );

  factory EmotionCondition.fromJson(Map<String, dynamic> json) {
    return EmotionCondition(
      tendency: EmotionTendency.values.firstWhere(
        (e) => e.name == json['parameters']['tendency'],
      ),
      threshold: json['parameters']['threshold']?.toDouble() ?? 0.6,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'parameters': parameters,
    };
  }
}

/// 语速条件
class SpeechRateCondition extends ResonanceCondition {
  final double minWordsPerSecond;
  final double maxWordsPerSecond;

  SpeechRateCondition({
    this.minWordsPerSecond = 0.0,
    this.maxWordsPerSecond = 10.0,
  }) : super(
          type: ResonanceConditionType.speechRate,
          parameters: {
            'minWordsPerSecond': minWordsPerSecond,
            'maxWordsPerSecond': maxWordsPerSecond,
          },
        );

  factory SpeechRateCondition.fromJson(Map<String, dynamic> json) {
    return SpeechRateCondition(
      minWordsPerSecond: json['parameters']['minWordsPerSecond']?.toDouble() ?? 0.0,
      maxWordsPerSecond: json['parameters']['maxWordsPerSecond']?.toDouble() ?? 10.0,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'parameters': parameters,
    };
  }
}

/// 规则动作
class ResonanceAction {
  final ResonanceActionType type;
  final List<String> responsePool;
  final Map<String, dynamic> metadata;

  const ResonanceAction({
    required this.type,
    required this.responsePool,
    this.metadata = const {},
  });

  factory ResonanceAction.fromJson(Map<String, dynamic> json) {
    return ResonanceAction(
      type: ResonanceActionType.values.firstWhere(
        (e) => e.name == json['type'],
      ),
      responsePool: List<String>.from(json['responsePool']),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'responsePool': responsePool,
      'metadata': metadata,
    };
  }
}

/// 共鸣规则
class ResonanceRule {
  final String id;
  final String name;
  final String description;
  final ResonanceCondition condition;
  final ResonanceAction action;
  final int priority;
  final Duration cooldown;
  final bool enabled;

  const ResonanceRule({
    required this.id,
    required this.name,
    required this.description,
    required this.condition,
    required this.action,
    this.priority = 1,
    this.cooldown = const Duration(seconds: 10),
    this.enabled = true,
  });

  factory ResonanceRule.fromJson(Map<String, dynamic> json) {
    return ResonanceRule(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      condition: ResonanceCondition.fromJson(json['condition']),
      action: ResonanceAction.fromJson(json['action']),
      priority: json['priority'] ?? 1,
      cooldown: Duration(seconds: json['cooldownSeconds'] ?? 10),
      enabled: json['enabled'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'condition': condition.toJson(),
      'action': action.toJson(),
      'priority': priority,
      'cooldownSeconds': cooldown.inSeconds,
      'enabled': enabled,
    };
  }
}

/// 规则配置集合
class ResonanceRuleSet {
  final String version;
  final String name;
  final String description;
  final List<ResonanceRule> rules;

  const ResonanceRuleSet({
    required this.version,
    required this.name,
    required this.description,
    required this.rules,
  });

  factory ResonanceRuleSet.fromJson(Map<String, dynamic> json) {
    return ResonanceRuleSet(
      version: json['version'],
      name: json['name'],
      description: json['description'],
      rules: (json['rules'] as List)
          .map((rule) => ResonanceRule.fromJson(rule))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'version': version,
      'name': name,
      'description': description,
      'rules': rules.map((rule) => rule.toJson()).toList(),
    };
  }
}
