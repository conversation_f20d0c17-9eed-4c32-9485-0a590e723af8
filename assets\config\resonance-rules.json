{"version": "1.0.0", "name": "默认共鸣规则集", "description": "Echo Cave AI陪伴功能的默认共鸣触发规则配置", "rules": [{"id": "keyword_tired", "name": "疲惫关键词触发", "description": "检测用户表达疲惫情绪的关键词", "condition": {"type": "keyword", "parameters": {"keywords": ["好累", "累死了", "太累了", "疲惫", "筋疲力尽", "累得不行"], "caseSensitive": false, "exactMatch": false}}, "action": {"type": "empathyResponse", "responsePool": ["嗯，听起来你很累呢", "我能感受到你的疲惫", "辛苦了", "是啊，确实很累的样子", "嗯嗯，我理解"], "metadata": {"emotion": "tired", "supportive": true}}, "priority": 2, "cooldownSeconds": 15, "enabled": true}, {"id": "keyword_frustrated", "name": "挫折关键词触发", "description": "检测用户表达挫折情绪的关键词", "condition": {"type": "keyword", "parameters": {"keywords": ["好烦", "烦死了", "太难了", "做不到", "不行了", "崩溃", "受不了"], "caseSensitive": false, "exactMatch": false}}, "action": {"type": "empathyResponse", "responsePool": ["嗯，我听到了你的烦恼", "这确实很让人沮丧", "我理解你的感受", "是的，有时候确实很难", "嗯嗯，我在听"], "metadata": {"emotion": "frustrated", "supportive": true}}, "priority": 2, "cooldownSeconds": 12, "enabled": true}, {"id": "keyword_sad", "name": "悲伤关键词触发", "description": "检测用户表达悲伤情绪的关键词", "condition": {"type": "keyword", "parameters": {"keywords": ["难过", "伤心", "痛苦", "心痛", "委屈", "想哭", "眼泪"], "caseSensitive": false, "exactMatch": false}}, "action": {"type": "comfort", "responsePool": ["我能感受到你的难过", "嗯，这确实很让人心痛", "我在这里陪着你", "让情绪流淌出来吧", "我理解你的感受"], "metadata": {"emotion": "sad", "supportive": true, "gentle": true}}, "priority": 3, "cooldownSeconds": 20, "enabled": true}, {"id": "pause_short", "name": "短暂停顿触发", "description": "检测用户说话中的短暂停顿，给予轻微回应", "condition": {"type": "pause", "parameters": {"minDurationMs": 1500, "maxDurationMs": 4000}}, "action": {"type": "acknowledgment", "responsePool": ["嗯", "嗯嗯", "我在听", "继续说", "然后呢"], "metadata": {"subtle": true, "encouraging": true}}, "priority": 1, "cooldownSeconds": 8, "enabled": true}, {"id": "pause_long", "name": "长时间停顿触发", "description": "检测用户较长时间的停顿，给予更明确的回应", "condition": {"type": "pause", "parameters": {"minDurationMs": 4000, "maxDurationMs": 10000}}, "action": {"type": "encouragement", "responsePool": ["我在这里听着", "慢慢说，不着急", "我陪着你", "想说什么都可以", "我理解，慢慢来"], "metadata": {"patient": true, "supportive": true}}, "priority": 2, "cooldownSeconds": 15, "enabled": true}, {"id": "emotion_negative", "name": "消极情绪检测", "description": "通过情绪分析检测消极倾向的表达", "condition": {"type": "emotion", "parameters": {"tendency": "negative", "threshold": 0.7}}, "action": {"type": "empathyResponse", "responsePool": ["我能感受到你现在的情绪", "这听起来确实不容易", "我理解你的感受", "嗯，我在听", "你的感受是完全可以理解的"], "metadata": {"emotion": "negative", "validating": true}}, "priority": 2, "cooldownSeconds": 18, "enabled": true}, {"id": "speech_rate_slow", "name": "语速缓慢检测", "description": "检测用户说话语速明显放慢，可能表示沉思或情绪低落", "condition": {"type": "speechRate", "parameters": {"minWordsPerSecond": 0.0, "maxWordsPerSecond": 1.5}}, "action": {"type": "acknowledgment", "responsePool": ["嗯，我在听", "慢慢说", "我理解", "不着急", "我陪着你"], "metadata": {"patient": true, "gentle": true}}, "priority": 1, "cooldownSeconds": 12, "enabled": true}]}