import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../services/parallel_audio_io_service.dart';
import '../services/stt/optimized_audio_stt_bridge.dart';
import '../services/stt/audio_activity_detector.dart';
import '../services/stt/stt_models.dart';
import '../services/stt/stt_config_service.dart';

/// 成本优化STT测试界面
class CostOptimizedSttTestView extends HookConsumerWidget {
  const CostOptimizedSttTestView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 服务实例
    final audioService = useMemoized(() => ParallelAudioIOService());
    final sttBridge = useMemoized(() => OptimizedAudioSttBridge(audioService));

    // 状态管理
    final isInitialized = useState(false);
    final isRecognizing = useState(false);
    final recognitionResults = useState<List<SttResult>>([]);
    final errorMessages = useState<List<SttError>>([]);
    final costStats = useState<Map<String, dynamic>>({});
    final selectedConfig = useState<String>('default');

    // 定时更新统计信息
    useEffect(() {
      Timer? timer;
      if (isRecognizing.value) {
        timer = Timer.periodic(const Duration(seconds: 1), (_) {
          costStats.value = sttBridge.costSavingStats;
        });
      }
      return () => timer?.cancel();
    }, [isRecognizing.value]);

    // 初始化
    useEffect(() {
      _initializeServices(sttBridge, isInitialized);

      // 监听识别结果
      final resultSubscription = sttBridge.onResult.listen((result) {
        recognitionResults.value = [...recognitionResults.value, result];
      });

      // 监听错误
      final errorSubscription = sttBridge.onError.listen((error) {
        errorMessages.value = [...errorMessages.value, error];
      });

      return () {
        resultSubscription.cancel();
        errorSubscription.cancel();
        sttBridge.dispose();
      };
    }, []);

    return Scaffold(
      backgroundColor: const Color(0xFF0F0F23),
      appBar: AppBar(
        title: const Text('成本优化STT测试'),
        backgroundColor: const Color(0xFF16213E),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // 成本节省统计卡片
            _buildCostSavingCard(costStats.value, sttBridge),

            const SizedBox(height: 16),

            // 配置选择
            _buildConfigSelection(selectedConfig, sttBridge),

            const SizedBox(height: 16),

            // 控制按钮
            _buildControlButtons(
              sttBridge,
              isInitialized.value,
              isRecognizing,
              recognitionResults,
              errorMessages,
              costStats,
            ),

            const SizedBox(height: 16),

            // 结果显示区域
            Expanded(
              child: _buildResultsDisplay(
                  recognitionResults.value, errorMessages.value),
            ),
          ],
        ),
      ),
    );
  }

  /// 初始化服务
  Future<void> _initializeServices(
    OptimizedAudioSttBridge sttBridge,
    ValueNotifier<bool> isInitialized,
  ) async {
    try {
      debugPrint('开始初始化优化STT服务...');

      // 检查配置
      final configValidation = SttConfigService.validateConfig();
      debugPrint('STT配置验证结果: $configValidation');

      final success = await sttBridge.initialize();
      debugPrint('STT服务初始化结果: $success');

      isInitialized.value = success;

      if (!success) {
        debugPrint('STT服务初始化失败，请检查配置');
      }
    } catch (e) {
      debugPrint('初始化优化STT服务失败: $e');
      isInitialized.value = false;
    }
  }

  /// 构建成本节省统计卡片
  Widget _buildCostSavingCard(
      Map<String, dynamic> stats, OptimizedAudioSttBridge sttBridge) {
    final savingRatio = stats['savingRatio'] ?? 0.0;
    final savedBytes = stats['savedBytes'] ?? 0;
    final totalBytes = stats['totalAudioBytes'] ?? 0;
    final isVoiceActive = sttBridge.isVoiceActive;
    final isSttConnected = sttBridge.isSttConnected;

    return Card(
      color: const Color(0xFF16213E),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.savings,
                  color: Colors.green,
                ),
                const SizedBox(width: 8),
                const Text(
                  '成本节省统计',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // 节省比例
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('成本节省',
                          style: TextStyle(color: Colors.white70)),
                      Text(
                        '${(savingRatio * 100).toStringAsFixed(1)}%',
                        style: const TextStyle(
                          color: Colors.green,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('节省数据',
                          style: TextStyle(color: Colors.white70)),
                      Text(
                        '${(savedBytes / 1024).toStringAsFixed(1)} KB',
                        style: const TextStyle(
                          color: Colors.green,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),
            const Divider(color: Colors.white24),
            const SizedBox(height: 8),

            // 实时状态
            _buildStatusRow('语音活动', isVoiceActive ? '检测中' : '静音'),
            _buildStatusRow('STT连接', isSttConnected ? '已连接' : '未连接'),
            _buildStatusRow(
                '总数据量', '${(totalBytes / 1024).toStringAsFixed(1)} KB'),
            _buildStatusRow('已发送',
                '${((totalBytes - savedBytes) / 1024).toStringAsFixed(1)} KB'),
          ],
        ),
      ),
    );
  }

  /// 构建配置选择
  Widget _buildConfigSelection(
      ValueNotifier<String> selectedConfig, OptimizedAudioSttBridge sttBridge) {
    return Card(
      color: const Color(0xFF16213E),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '优化配置',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            DropdownButton<String>(
              value: selectedConfig.value,
              dropdownColor: const Color(0xFF16213E),
              style: const TextStyle(color: Colors.white),
              items: const [
                DropdownMenuItem(value: 'default', child: Text('默认配置')),
                DropdownMenuItem(
                    value: 'sensitive', child: Text('敏感配置（更容易检测）')),
                DropdownMenuItem(
                    value: 'conservative', child: Text('保守配置（减少误检测）')),
                DropdownMenuItem(
                    value: 'cost_saving', child: Text('节省配置（最大化节省）')),
              ],
              onChanged: (value) {
                if (value != null) {
                  selectedConfig.value = value;
                  _updateConfig(sttBridge, value);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 更新配置
  void _updateConfig(OptimizedAudioSttBridge sttBridge, String configType) {
    AudioActivityConfig config;

    switch (configType) {
      case 'sensitive':
        config = AudioActivityConfig.sensitive();
        break;
      case 'conservative':
        config = AudioActivityConfig.conservative();
        break;
      case 'cost_saving':
        config = AudioActivityConfig.costSaving();
        break;
      default:
        config = const AudioActivityConfig();
    }

    sttBridge.updateActivityConfig(config);
  }

  /// 构建状态行
  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(color: Colors.white70),
          ),
          Text(
            value,
            style: const TextStyle(color: Colors.white),
          ),
        ],
      ),
    );
  }

  /// 构建控制按钮
  Widget _buildControlButtons(
    OptimizedAudioSttBridge sttBridge,
    bool isInitialized,
    ValueNotifier<bool> isRecognizing,
    ValueNotifier<List<SttResult>> recognitionResults,
    ValueNotifier<List<SttError>> errorMessages,
    ValueNotifier<Map<String, dynamic>> costStats,
  ) {
    return Row(
      children: [
        // 开始识别按钮
        Expanded(
          child: ElevatedButton.icon(
            onPressed: isInitialized && !isRecognizing.value
                ? () => _startRecognition(sttBridge, isRecognizing, costStats)
                : null,
            icon: const Icon(Icons.mic),
            label: const Text('开始识别'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),

        const SizedBox(width: 12),

        // 停止识别按钮
        Expanded(
          child: ElevatedButton.icon(
            onPressed: isRecognizing.value
                ? () => _stopRecognition(sttBridge, isRecognizing, costStats)
                : null,
            icon: const Icon(Icons.stop),
            label: const Text('停止'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),

        const SizedBox(width: 12),

        // 清除结果按钮
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () =>
                _clearResults(recognitionResults, errorMessages, costStats),
            icon: const Icon(Icons.clear),
            label: const Text('清除'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  /// 开始识别
  Future<void> _startRecognition(
    OptimizedAudioSttBridge sttBridge,
    ValueNotifier<bool> isRecognizing,
    ValueNotifier<Map<String, dynamic>> costStats,
  ) async {
    try {
      await sttBridge.startRecognition();
      isRecognizing.value = true;
      costStats.value = {}; // 重置统计
    } catch (e) {
      debugPrint('开始优化识别失败: $e');
    }
  }

  /// 停止识别
  Future<void> _stopRecognition(
    OptimizedAudioSttBridge sttBridge,
    ValueNotifier<bool> isRecognizing,
    ValueNotifier<Map<String, dynamic>> costStats,
  ) async {
    try {
      await sttBridge.stopRecognition();
      isRecognizing.value = false;
      costStats.value = sttBridge.costSavingStats; // 获取最终统计
    } catch (e) {
      debugPrint('停止优化识别失败: $e');
    }
  }

  /// 清除结果
  void _clearResults(
    ValueNotifier<List<SttResult>> recognitionResults,
    ValueNotifier<List<SttError>> errorMessages,
    ValueNotifier<Map<String, dynamic>> costStats,
  ) {
    recognitionResults.value = [];
    errorMessages.value = [];
    costStats.value = {};
  }

  /// 构建结果显示区域
  Widget _buildResultsDisplay(List<SttResult> results, List<SttError> errors) {
    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          const TabBar(
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white54,
            indicatorColor: Colors.green,
            tabs: [
              Tab(text: '识别结果'),
              Tab(text: '错误日志'),
            ],
          ),
          Expanded(
            child: TabBarView(
              children: [
                // 识别结果页
                _buildResultsList(results),

                // 错误日志页
                _buildErrorsList(errors),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建识别结果列表
  Widget _buildResultsList(List<SttResult> results) {
    if (results.isEmpty) {
      return const Center(
        child: Text(
          '暂无识别结果\n开始说话测试成本优化效果',
          textAlign: TextAlign.center,
          style: TextStyle(color: Colors.white54),
        ),
      );
    }

    return ListView.builder(
      itemCount: results.length,
      itemBuilder: (context, index) {
        final result = results[index];
        return Card(
          color: const Color(0xFF1A1A2E),
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: ListTile(
            leading: Icon(
              result.isFinal
                  ? Icons.check_circle
                  : Icons.radio_button_unchecked,
              color: result.isFinal ? Colors.green : Colors.orange,
            ),
            title: Text(
              result.text,
              style: const TextStyle(color: Colors.white),
            ),
            subtitle: Text(
              '置信度: ${(result.confidence * 100).toStringAsFixed(1)}% | ${result.timestamp.toString().substring(11, 19)}',
              style: const TextStyle(color: Colors.white54, fontSize: 12),
            ),
          ),
        );
      },
    );
  }

  /// 构建错误列表
  Widget _buildErrorsList(List<SttError> errors) {
    if (errors.isEmpty) {
      return const Center(
        child: Text(
          '暂无错误记录',
          style: TextStyle(color: Colors.white54),
        ),
      );
    }

    return ListView.builder(
      itemCount: errors.length,
      itemBuilder: (context, index) {
        final error = errors[index];
        return Card(
          color: const Color(0xFF2D1B1B),
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: ListTile(
            leading: const Icon(Icons.error, color: Colors.red),
            title: Text(
              error.message,
              style: const TextStyle(color: Colors.white),
            ),
            subtitle: Text(
              '错误码: ${error.code} | ${error.timestamp.toString().substring(11, 19)}',
              style: const TextStyle(color: Colors.white54, fontSize: 12),
            ),
          ),
        );
      },
    );
  }
}
