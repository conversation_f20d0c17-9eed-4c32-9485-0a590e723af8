# 🎮 Task 24 共鸣引擎演示界面使用指南

## 🚀 如何启动演示界面

### 方法1: 通过应用内开发者选项（推荐）

1. **启动应用**
   ```bash
   flutter run
   ```

2. **进入开发者选项**
   - 在主界面点击底部的 **"开发者模式"** 按钮
   - 在弹出的开发者选项对话框中找到 **"🧠 Task24 共鸣引擎"** 按钮
   - 点击该按钮即可进入演示界面

### 方法2: 直接导航到演示路由

如果您想在代码中直接导航到演示界面：

```dart
Navigator.pushNamed(context, '/resonance-demo');
```

## 🎯 演示界面功能

### 界面布局

演示界面分为以下几个部分：

1. **引擎状态区域**
   - 显示当前引擎状态（未初始化/正在初始化/运行中/失败）
   - 提供初始化按钮

2. **测试用例区域**
   - 提供多个预设的测试按钮
   - 每个按钮测试不同的功能

3. **结果显示区域**
   - 左侧：测试结果日志
   - 右侧：触发的回应事件

### 测试用例说明

#### 🔧 **测试疲惫关键词**
- **输入**: "我今天好累啊"
- **期望**: 触发疲惫检测规则
- **回应**: "我理解你的疲惫" / "休息一下吧" / "辛苦了"

#### 😢 **测试负面情绪**
- **输入**: "我很难过，感觉很痛苦"
- **期望**: 触发情绪分析规则（AI增强）
- **回应**: "我在这里陪着你" / "别难过" / "会好起来的"

#### ⏸️ **测试停顿检测**
- **输入**: "我想说..." + 2秒停顿
- **期望**: 触发停顿检测规则
- **回应**: "嗯" / "我在听" / "继续说吧"

#### 🔄 **测试复合条件**
- **输入**: "我真的很累，不想说话了"
- **期望**: 可能触发多个规则（疲惫+情绪）
- **回应**: 根据优先级选择最合适的回应

#### 🧹 **清除结果**
- 清空所有测试结果和回应事件记录

## 📊 监控信息

### 测试结果日志
显示每次测试的详细信息：
- ✅ 成功操作
- ❌ 失败操作  
- 🧪 测试开始
- 📝 输入文本

### 回应事件
显示每个触发的回应事件：
- **规则名称**: 触发的规则
- **回应文本**: AI选择的回应
- **时间戳**: 触发时间
- **元数据**: 选择策略、置信度等

## 🔍 调试信息

演示界面会显示详细的调试信息，包括：

- **引擎状态变化**
- **规则匹配过程**
- **AI分析结果**（如果API可用）
- **回应选择策略**
- **TTS播放状态**

## ⚠️ 注意事项

### AI功能限制
- **DeepSeek API**: 如果未配置API密钥，AI分析会自动回退到传统分析
- **网络连接**: AI功能需要网络连接

### 测试环境
- 演示使用Mock STT服务，不需要真实的语音输入
- TTS功能在演示中被模拟，不会实际播放声音

### 性能监控
- 观察响应时间和内存使用
- 检查是否有错误或异常

## 🎉 成功标准

演示成功运行的标志：

1. ✅ **引擎初始化成功**
2. ✅ **测试用例能正常触发**
3. ✅ **回应事件正确显示**
4. ✅ **无错误或异常**
5. ✅ **响应时间合理**（< 500ms）

## 🐛 故障排除

### 常见问题

1. **引擎初始化失败**
   - 检查依赖服务是否正常
   - 查看错误日志

2. **测试用例无响应**
   - 确认引擎已成功初始化
   - 检查规则配置是否正确

3. **AI分析失败**
   - 这是正常的，系统会自动回退到传统分析
   - 检查网络连接和API配置

### 调试技巧

1. **查看控制台输出**
   ```bash
   flutter run --verbose
   ```

2. **检查日志信息**
   - 观察演示界面的测试结果日志
   - 注意任何错误或警告信息

3. **重新初始化**
   - 如果遇到问题，可以重新启动应用
   - 或者清除结果后重新测试

---

🎊 **祝您测试愉快！Task 24 共鸣引擎已经准备好为您的AI陪伴应用提供强大的智能回应能力！**
