import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import '../parallel_audio_io_service.dart';
import 'streaming_stt_service.dart';
import 'stt_models.dart';
import 'stt_config_service.dart';
import 'realtime_stt_processor.dart';
import 'stt_error_recovery_manager.dart';

/// AEC-STT桥接服务
/// 
/// 专门用于将AEC处理后的音频流连接到STT服务，实现高质量的实时语音转文字
class AecSttBridge extends ChangeNotifier {
  // 服务依赖
  final ParallelAudioIOService _aecService;
  StreamingSttService? _sttService;
  RealtimeSttProcessor? _resultProcessor;
  SttErrorRecoveryManager? _errorRecoveryManager;

  // 状态管理
  bool _isActive = false;
  bool _isInitialized = false;
  bool _isSttConnected = false;
  StreamSubscription<Uint8List>? _audioSubscription;
  StreamSubscription<SttResult>? _sttResultSubscription;
  StreamSubscription<String>? _segmentSubscription;
  StreamSubscription<SttServiceHealth>? _healthSubscription;
  StreamSubscription<SttRecoveryAction>? _recoverySubscription;
  
  // 事件流控制器
  final StreamController<SttResult> _resultController = StreamController<SttResult>.broadcast();
  final StreamController<SttResult> _processedResultController = StreamController<SttResult>.broadcast();
  final StreamController<String> _segmentController = StreamController<String>.broadcast();
  final StreamController<SttError> _errorController = StreamController<SttError>.broadcast();
  final StreamController<SttConnectionState> _stateController = StreamController<SttConnectionState>.broadcast();
  final StreamController<SttServiceHealth> _healthController = StreamController<SttServiceHealth>.broadcast();
  final StreamController<SttRecoveryAction> _recoveryActionController = StreamController<SttRecoveryAction>.broadcast();
  
  // 配置
  SttConfig _config = SttConfigService.createDefaultConfig();
  
  // 统计信息
  int _totalAudioBytes = 0;
  int _processedAudioBytes = 0;
  DateTime? _sessionStartTime;
  DateTime? _lastAudioTime;
  int _resultCount = 0;
  int _errorCount = 0;

  AecSttBridge(this._aecService);

  /// 当前状态
  bool get isActive => _isActive;
  bool get isInitialized => _isInitialized;
  bool get hasAecService => _aecService.isInitialized;
  bool get hasSttService => _sttService != null;
  bool get isSttConnected => _isSttConnected;
  
  /// 配置信息
  SttConfig get config => _config;
  
  /// 事件流
  Stream<SttResult> get onResult => _resultController.stream;
  Stream<SttResult> get onProcessedResult => _processedResultController.stream;
  Stream<String> get onSegmentComplete => _segmentController.stream;
  Stream<SttError> get onError => _errorController.stream;
  Stream<SttConnectionState> get onConnectionStateChanged => _stateController.stream;
  Stream<SttServiceHealth> get onHealthChanged => _healthController.stream;
  Stream<SttRecoveryAction> get onRecoveryAction => _recoveryActionController.stream;
  
  /// 统计信息
  Map<String, dynamic> get stats => {
    'isActive': _isActive,
    'isInitialized': _isInitialized,
    'isSttConnected': _isSttConnected,
    'totalAudioBytes': _totalAudioBytes,
    'processedAudioBytes': _processedAudioBytes,
    'sessionDuration': _sessionStartTime != null 
        ? DateTime.now().difference(_sessionStartTime!).inMilliseconds / 1000.0 
        : 0.0,
    'resultCount': _resultCount,
    'errorCount': _errorCount,
    'lastAudioTime': _lastAudioTime?.toString(),
    'audioProcessingRate': _totalAudioBytes > 0 
        ? (_processedAudioBytes / _totalAudioBytes * 100).toStringAsFixed(1) + '%'
        : '0%',
  };

  /// 初始化桥接服务
  Future<bool> initialize({SttConfig? config}) async {
    try {
      if (config != null) {
        _config = config;
      }

      // 确保AEC服务已初始化
      if (!_aecService.isInitialized) {
        final aecInitialized = await _aecService.initialize();
        if (!aecInitialized) {
          debugPrint('AEC服务初始化失败');
          return false;
        }
      }

      // 创建STT服务
      _sttService = SttConfigService.createDefaultService();
      if (_sttService == null) {
        debugPrint('无法创建STT服务，请检查配置');
        return false;
      }

      // 初始化STT服务
      final sttInitialized = await _sttService!.initialize(_config);
      if (!sttInitialized) {
        debugPrint('STT服务初始化失败');
        return false;
      }

      // 创建实时结果处理器
      _resultProcessor = RealtimeSttProcessor(
        confidenceThreshold: _config.confidenceThreshold ?? 0.6,
        maxCacheSize: 100,
        segmentTimeout: const Duration(seconds: 3),
        duplicateWindow: const Duration(milliseconds: 500),
      );

      // 创建错误恢复管理器
      _errorRecoveryManager = SttErrorRecoveryManager(
        maxRetryAttempts: 5,
        baseRetryDelay: const Duration(seconds: 1),
        maxRetryDelay: const Duration(seconds: 30),
        healthCheckInterval: const Duration(seconds: 10),
        backoffMultiplier: 2.0,
      );

      // 监听STT事件
      _sttService!.onResult.listen(_onSttResult);
      _sttService!.onError.listen(_onSttError);
      _sttService!.onConnectionStateChanged.listen(_onSttStateChanged);

      // 监听处理器事件
      _sttResultSubscription = _resultProcessor!.onProcessedResult.listen(_onProcessedResult);
      _segmentSubscription = _resultProcessor!.onSegmentComplete.listen(_onSegmentComplete);

      // 监听错误恢复管理器事件
      _healthSubscription = _errorRecoveryManager!.onHealthChanged.listen(_onHealthChanged);
      _recoverySubscription = _errorRecoveryManager!.onRecoveryAction.listen(_onRecoveryAction);

      _isInitialized = true;
      notifyListeners();
      
      debugPrint('AEC-STT桥接服务初始化成功');
      return true;

    } catch (e) {
      debugPrint('AEC-STT桥接服务初始化失败: $e');
      return false;
    }
  }

  /// 开始实时语音识别
  Future<void> startRecognition() async {
    if (!_isInitialized || _isActive) {
      debugPrint('桥接服务未初始化或已在运行中');
      return;
    }

    try {
      // 启动AEC音频流录制
      await _aecService.startStreamRecording();
      
      // 订阅AEC处理后的音频流
      if (_aecService.audioStream != null) {
        _audioSubscription = _aecService.audioStream!.listen(
          _onAudioData,
          onError: _onAudioError,
          onDone: _onAudioDone,
        );
      } else {
        throw Exception('AEC音频流不可用');
      }

      // 启动STT监听
      await _sttService!.startListening();
      _isSttConnected = true;

      _isActive = true;
      _sessionStartTime = DateTime.now();
      _totalAudioBytes = 0;
      _processedAudioBytes = 0;
      _resultCount = 0;
      _errorCount = 0;

      // 启动结果处理器会话
      _resultProcessor?.startSession();

      // 启动错误恢复管理器健康监控
      _errorRecoveryManager?.startHealthMonitoring();

      notifyListeners();
      debugPrint('开始AEC-STT实时语音识别');

    } catch (e) {
      debugPrint('启动语音识别失败: $e');
      _emitError(SttErrorType.unknown, 'START_FAILED', '启动识别失败: $e', e);
      rethrow;
    }
  }

  /// 停止实时语音识别
  Future<void> stopRecognition() async {
    if (!_isActive) return;

    try {
      // 停止音频流订阅
      await _audioSubscription?.cancel();
      _audioSubscription = null;

      // 停止STT监听
      if (_sttService != null && _isSttConnected) {
        await _sttService!.stopListening();
        _isSttConnected = false;
      }

      // 停止AEC音频流
      await _aecService.stopStreamRecording();

      // 结束结果处理器会话
      _resultProcessor?.endSession();

      // 停止错误恢复管理器健康监控
      _errorRecoveryManager?.stopHealthMonitoring();

      _isActive = false;
      notifyListeners();

      debugPrint('AEC-STT语音识别已停止');

    } catch (e) {
      debugPrint('停止语音识别失败: $e');
      _emitError(SttErrorType.unknown, 'STOP_FAILED', '停止识别失败: $e', e);
    }
  }

  /// 更新配置
  Future<void> updateConfig(SttConfig newConfig) async {
    _config = newConfig;
    
    if (_sttService != null) {
      await _sttService!.reset();
      await _sttService!.initialize(_config);
    }
    
    notifyListeners();
    debugPrint('STT配置已更新');
  }

  /// 获取配置验证结果
  Map<String, dynamic> validateConfiguration() {
    return SttConfigService.validateConfig();
  }

  /// 获取配置摘要
  Map<String, String> getConfigSummary() {
    final sttConfig = SttConfigService.getConfigSummary();
    final aecConfig = {
      'AEC服务状态': _aecService.isInitialized ? '已初始化' : '未初始化',
      '录音状态': _aecService.isRecording ? '录音中' : '未录音',
      '播放状态': _aecService.isPlaying ? '播放中' : '空闲',
      '同时模式': _aecService.isSimultaneousMode ? '启用' : '禁用',
    };
    
    return {...sttConfig, ...aecConfig};
  }

  /// 处理AEC处理后的音频数据
  void _onAudioData(Uint8List audioData) {
    if (!_isActive || _sttService == null || !_isSttConnected) return;

    try {
      // 更新统计信息
      _totalAudioBytes += audioData.length;
      _lastAudioTime = DateTime.now();
      
      // 发送音频数据到STT服务
      _sttService!.sendAudioData(audioData);
      _processedAudioBytes += audioData.length;
      
    } catch (e) {
      debugPrint('处理AEC音频数据失败: $e');
      _emitError(SttErrorType.audioFormat, 'AUDIO_PROCESS_FAILED', '音频处理失败: $e', e);
    }
  }

  /// 处理音频错误
  void _onAudioError(error) {
    debugPrint('AEC音频流错误: $error');
    _emitError(SttErrorType.audioFormat, 'AEC_AUDIO_STREAM_ERROR', 'AEC音频流错误: $error', error);
  }

  /// 音频流结束
  void _onAudioDone() {
    debugPrint('AEC音频流结束');
    stopRecognition();
  }

  /// 处理STT识别结果
  void _onSttResult(SttResult result) {
    _resultCount++;
    _resultController.add(result);

    // 将原始结果发送到处理器进行精细化处理
    _resultProcessor?.processResult(result);

    debugPrint('STT原始结果: ${result.text} (最终: ${result.isFinal}, 置信度: ${result.confidence})');
  }

  /// 处理精细化处理后的结果
  void _onProcessedResult(SttResult result) {
    _processedResultController.add(result);
    debugPrint('STT处理后结果: ${result.text} (最终: ${result.isFinal}, 置信度: ${result.confidence})');
  }

  /// 处理完成的段落
  void _onSegmentComplete(String segment) {
    _segmentController.add(segment);

    // 记录成功操作
    _errorRecoveryManager?.recordSuccess();

    debugPrint('STT完成段落: $segment');
  }

  /// 处理健康状态变化
  void _onHealthChanged(SttServiceHealth health) {
    _healthController.add(health);
    debugPrint('STT服务健康状态: $health');
  }

  /// 处理恢复动作
  void _onRecoveryAction(SttRecoveryAction action) {
    _recoveryActionController.add(action);
    debugPrint('STT恢复动作: ${action.type} - ${action.reason}');
  }

  /// 处理STT错误
  void _onSttError(SttError error) async {
    _errorCount++;
    _errorController.add(error);

    // 使用错误恢复管理器处理错误
    if (_errorRecoveryManager != null) {
      final recoveryAction = await _errorRecoveryManager!.recordError(error);

      // 根据恢复策略执行相应操作
      if (recoveryAction.type == SttRecoveryActionType.retry) {
        debugPrint('STT错误恢复: 将在 ${recoveryAction.delay?.inSeconds ?? 0} 秒后重试');
        await _errorRecoveryManager!.executeRetry(() async {
          // 重新启动STT连接
          await _sttService?.disconnect();
          await _sttService?.startListening();
        });
      } else if (recoveryAction.type == SttRecoveryActionType.fail) {
        debugPrint('STT错误恢复: ${recoveryAction.reason}');
        // 可以在这里触发更高级别的错误处理
      }
    }

    debugPrint('STT错误: ${error.message}');
  }

  /// 处理STT连接状态变化
  void _onSttStateChanged(SttConnectionState state) {
    _stateController.add(state);
    debugPrint('STT连接状态变化: $state');
    
    // 更新内部连接状态
    _isSttConnected = (state == SttConnectionState.listening);
    notifyListeners();
  }

  /// 发送错误事件
  void _emitError(SttErrorType type, String code, String message, [dynamic details]) {
    final error = SttError(
      type: type,
      code: code,
      message: message,
      timestamp: DateTime.now(),
      details: details,
    );
    _errorController.add(error);
  }

  @override
  void dispose() {
    stopRecognition();

    // 取消订阅
    _sttResultSubscription?.cancel();
    _segmentSubscription?.cancel();
    _healthSubscription?.cancel();
    _recoverySubscription?.cancel();

    // 关闭流控制器
    _resultController.close();
    _processedResultController.close();
    _segmentController.close();
    _errorController.close();
    _stateController.close();
    _healthController.close();
    _recoveryActionController.close();

    // 清理服务
    _resultProcessor?.dispose();
    _errorRecoveryManager?.dispose();
    _sttService?.dispose();

    super.dispose();
  }
}
