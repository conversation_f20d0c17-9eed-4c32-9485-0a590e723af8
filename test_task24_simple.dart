/// Task 24 简单测试脚本
/// 直接运行此脚本来测试共鸣引擎功能

import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'lib/services/resonance/resonance_engine_service.dart';
import 'lib/services/resonance/resonance_models.dart';
import 'lib/services/stt/streaming_stt_service.dart';
import 'lib/services/stt/stt_models.dart';

/// 简单的Mock STT服务
class SimpleMockSttService extends StreamingSttService {
  final StreamController<SttResult> _resultController = StreamController<SttResult>.broadcast();
  final StreamController<SttError> _errorController = StreamController<SttError>.broadcast();
  final StreamController<SttConnectionState> _stateController = StreamController<SttConnectionState>.broadcast();
  final StreamController<void> _endOfSpeechController = StreamController<void>.broadcast();
  
  SttConnectionState _state = SttConnectionState.disconnected;
  bool _isListening = false;

  @override
  SttConnectionState get connectionState => _state;

  @override
  SttConfig get config => SttConfig(
    language: 'zh-cn',
    sampleRate: 16000,
    enableInterimResults: true,
  );

  @override
  SttStats get stats => SttStats(
    totalDuration: 1000,
    totalCharacters: 100,
    averageConfidence: 0.9,
    networkLatency: 100,
    errorCount: 0,
  );

  @override
  bool get isListening => _isListening;

  @override
  Stream<SttResult> get onResult => _resultController.stream;

  @override
  Stream<SttError> get onError => _errorController.stream;

  @override
  Stream<SttConnectionState> get onConnectionStateChanged => _stateController.stream;

  @override
  Stream<void> get onEndOfSpeech => _endOfSpeechController.stream;

  @override
  Future<bool> initialize(SttConfig config) async {
    _state = SttConnectionState.connected;
    _stateController.add(_state);
    return true;
  }

  @override
  Future<void> startListening() async {
    _isListening = true;
    _state = SttConnectionState.listening;
    _stateController.add(_state);
  }

  @override
  Future<void> stopListening() async {
    _isListening = false;
    _state = SttConnectionState.connected;
    _stateController.add(_state);
  }

  @override
  void sendAudioData(Uint8List audioData) {}

  @override
  Future<void> reset() async {
    _isListening = false;
    _state = SttConnectionState.connected;
    _stateController.add(_state);
  }

  @override
  Future<void> disconnect() async {
    _isListening = false;
    _state = SttConnectionState.disconnected;
    _stateController.add(_state);
  }

  @override
  Future<List<String>> getSupportedLanguages() async {
    return ['zh-CN', 'en-US'];
  }

  @override
  Future<int> testConnection() async {
    return 100;
  }

  // 测试辅助方法
  void simulateResult(String text, {bool isFinal = false}) {
    final result = SttResult(
      text: text,
      isFinal: isFinal,
      confidence: 0.9,
      timestamp: DateTime.now(),
    );
    _resultController.add(result);
  }

  @override
  void dispose() {
    _resultController.close();
    _errorController.close();
    _stateController.close();
    _endOfSpeechController.close();
    super.dispose();
  }
}

void main() async {
  print('🚀 Task 24 共鸣引擎简单测试');
  print('================================');

  // 初始化Flutter绑定（测试环境需要）
  TestWidgetsFlutterBinding.ensureInitialized();

  final engine = ResonanceEngineService();
  final mockStt = SimpleMockSttService();
  
  try {
    print('📋 步骤1: 创建测试规则集...');
    
    final testRules = ResonanceRuleSet(
      version: '1.0.0',
      name: 'Simple Test Rules',
      description: 'Task 24 简单测试规则',
      rules: [
        ResonanceRule(
          id: 'tired_rule',
          name: '疲惫检测',
          description: '检测疲惫关键词',
          condition: KeywordCondition(keywords: ['累', '疲惫', '好累']),
          action: ResonanceAction(
            type: ResonanceActionType.empathyResponse,
            responsePool: ['我理解你的疲惫', '休息一下吧', '辛苦了'],
          ),
          priority: 2,
        ),
        ResonanceRule(
          id: 'sad_rule',
          name: '难过检测',
          description: '检测负面情绪',
          condition: EmotionCondition(
            tendency: EmotionTendency.negative,
            threshold: 0.6,
          ),
          action: ResonanceAction(
            type: ResonanceActionType.comfort,
            responsePool: ['我在这里陪着你', '别难过', '会好起来的'],
          ),
          priority: 3,
        ),
      ],
    );
    
    print('✅ 规则集创建成功');

    print('📋 步骤2: 设置引擎状态...');
    engine.setStateForTesting(ResonanceEngineState.initialized);
    print('✅ 引擎状态设置完成');

    print('📋 步骤3: 加载规则...');
    await engine.loadRules(testRules);
    print('✅ 规则加载成功');

    print('📋 步骤4: 启动引擎...');
    await engine.start(mockStt);
    print('✅ 引擎启动成功');

    print('📋 步骤5: 监听回应事件...');
    final responseEvents = <ResonanceResponseEvent>[];
    engine.onResponse.listen((event) {
      responseEvents.add(event);
      print('🎯 触发回应: "${event.responseText}" (规则: ${event.ruleName})');
    });

    print('📋 步骤6: 运行测试用例...');
    
    // 测试用例1: 疲惫关键词
    print('\n🧪 测试用例1: 疲惫关键词');
    mockStt.simulateResult('我今天好累啊', isFinal: true);
    await Future.delayed(Duration(milliseconds: 500));
    
    // 测试用例2: 负面情绪
    print('\n🧪 测试用例2: 负面情绪');
    mockStt.simulateResult('我很难过，感觉很痛苦', isFinal: true);
    await Future.delayed(Duration(milliseconds: 500));
    
    // 测试用例3: 复合条件
    print('\n🧪 测试用例3: 复合条件');
    mockStt.simulateResult('我真的很累，不想说话了', isFinal: true);
    await Future.delayed(Duration(milliseconds: 500));

    print('\n📊 测试结果总结:');
    print('总触发事件数: ${responseEvents.length}');
    
    if (responseEvents.isNotEmpty) {
      print('✅ Task 24 共鸣引擎功能正常！');
      print('\n触发的回应事件:');
      for (int i = 0; i < responseEvents.length; i++) {
        final event = responseEvents[i];
        print('  ${i + 1}. 规则: ${event.ruleName}');
        print('     回应: "${event.responseText}"');
        print('     时间: ${event.timestamp.toString().substring(11, 19)}');
      }
    } else {
      print('⚠️  没有触发任何回应事件，可能存在问题');
    }

    print('\n🎉 测试完成！');

  } catch (e, stackTrace) {
    print('❌ 测试失败: $e');
    print('堆栈跟踪: $stackTrace');
  } finally {
    await engine.stop();
    mockStt.dispose();
  }
}

/// 简单的测试绑定
class TestWidgetsFlutterBinding extends BindingBase with ServicesBinding {
  static TestWidgetsFlutterBinding? _instance;
  
  static TestWidgetsFlutterBinding get instance => _instance ??= TestWidgetsFlutterBinding._();
  
  TestWidgetsFlutterBinding._();
  
  static TestWidgetsFlutterBinding ensureInitialized() {
    if (_instance == null) {
      TestWidgetsFlutterBinding();
    }
    return instance;
  }
}
